#!/usr/bin/env node

/**
 * Comprehensive Security Audit Script
 * 
 * This script performs automated security checks on the ClassTasker application
 * to identify potential vulnerabilities and security misconfigurations.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Security audit results
const auditResults = {
  critical: [],
  high: [],
  medium: [],
  low: [],
  info: []
};

// Colors for console output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message, details = '') {
  const timestamp = new Date().toISOString();
  const color = {
    CRITICAL: colors.red,
    HIGH: colors.yellow,
    MEDIUM: colors.blue,
    LOW: colors.green,
    INFO: colors.reset
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
  if (details) {
    console.log(`  ${details}`);
  }
  
  auditResults[level.toLowerCase()].push({ message, details, timestamp });
}

/**
 * Check for exposed API keys and secrets
 */
function checkExposedSecrets() {
  log('INFO', 'Checking for exposed secrets...');
  
  const sensitiveFiles = [
    'public/env-config.js',
    'index.html',
    'vite.config.ts',
    'lovable.config.js'
  ];
  
  const secretPatterns = [
    { pattern: /sk_live_[a-zA-Z0-9]{24,}/, name: 'Stripe Live Secret Key' },
    { pattern: /sk_test_[a-zA-Z0-9]{24,}/, name: 'Stripe Test Secret Key' },
    { pattern: /AIza[0-9A-Za-z\\-_]{35}/, name: 'Google API Key' },
    { pattern: /eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*/, name: 'JWT Token' },
    { pattern: /[a-zA-Z0-9]{32,}/, name: 'Potential API Key' }
  ];
  
  sensitiveFiles.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      secretPatterns.forEach(({ pattern, name }) => {
        const matches = content.match(pattern);
        if (matches) {
          if (filePath.startsWith('public/')) {
            log('CRITICAL', `${name} found in public file: ${filePath}`, 
                `Exposed value: ${matches[0].substring(0, 20)}...`);
          } else {
            log('HIGH', `${name} found in: ${filePath}`, 
                `Value: ${matches[0].substring(0, 20)}...`);
          }
        }
      });
      
      // Check for hardcoded credentials
      if (content.includes('password') && content.includes('=')) {
        log('MEDIUM', `Potential hardcoded password in: ${filePath}`);
      }
    }
  });
}

/**
 * Check for debug and test routes
 */
function checkDebugRoutes() {
  log('INFO', 'Checking for debug and test routes...');
  
  const routeFiles = [
    'src/App.tsx',
    'src/routes/PWARoutes.tsx',
    'vite.config.ts'
  ];
  
  const debugPatterns = [
    { pattern: /\/test-/, name: 'Test Route' },
    { pattern: /\/debug/, name: 'Debug Route' },
    { pattern: /\/emergency/, name: 'Emergency Route' },
    { pattern: /error\.stack/, name: 'Stack Trace Exposure' },
    { pattern: /console\.log\(.*process\.env/, name: 'Environment Variable Logging' }
  ];
  
  routeFiles.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      debugPatterns.forEach(({ pattern, name }) => {
        const matches = content.match(pattern);
        if (matches) {
          log('HIGH', `${name} found in: ${filePath}`, 
              `Pattern: ${matches[0]}`);
        }
      });
    }
  });
}

/**
 * Check API endpoint security
 */
function checkAPIEndpoints() {
  log('INFO', 'Checking API endpoint security...');
  
  const apiFiles = [
    'server/api-server.js',
    'server/routes/getstream.js',
    'src/api/getstream/token.ts'
  ];
  
  apiFiles.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for missing authentication
      if (content.includes('app.post') || content.includes('router.post')) {
        if (!content.includes('auth') && !content.includes('token')) {
          log('HIGH', `Potentially unauthenticated endpoint in: ${filePath}`);
        }
      }
      
      // Check for missing input validation
      if (content.includes('req.body') && !content.includes('validate')) {
        log('MEDIUM', `Missing input validation in: ${filePath}`);
      }
      
      // Check for missing rate limiting
      if (!content.includes('rateLimit') && !content.includes('rate-limit')) {
        log('MEDIUM', `Missing rate limiting in: ${filePath}`);
      }
    }
  });
}

/**
 * Check environment variable security
 */
function checkEnvironmentVariables() {
  log('INFO', 'Checking environment variable security...');
  
  const envFiles = ['.env.example', 'scripts/.env.example'];
  
  envFiles.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      // Check for actual secrets in example files
      if (content.includes('sk_') || content.includes('AIza')) {
        log('CRITICAL', `Real API keys found in example file: ${filePath}`);
      }
      
      // Check for missing VITE_ prefix on client variables
      const lines = content.split('\n');
      lines.forEach((line, index) => {
        if (line.includes('SUPABASE_ANON_KEY') && !line.startsWith('VITE_')) {
          log('MEDIUM', `Client-side variable missing VITE_ prefix in ${filePath}:${index + 1}`);
        }
      });
    }
  });
}

/**
 * Check for security headers and configurations
 */
function checkSecurityHeaders() {
  log('INFO', 'Checking security headers and configurations...');
  
  const configFiles = [
    'vite.config.ts',
    'server/index.js',
    'server/api-server.js'
  ];
  
  const securityHeaders = [
    'Content-Security-Policy',
    'X-Frame-Options',
    'X-Content-Type-Options',
    'Strict-Transport-Security'
  ];
  
  configFiles.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    if (fs.existsSync(fullPath)) {
      const content = fs.readFileSync(fullPath, 'utf8');
      
      securityHeaders.forEach(header => {
        if (!content.includes(header)) {
          log('MEDIUM', `Missing security header ${header} in: ${filePath}`);
        }
      });
      
      // Check for CORS configuration
      if (content.includes('cors()') && !content.includes('origin:')) {
        log('MEDIUM', `Permissive CORS configuration in: ${filePath}`);
      }
    }
  });
}

/**
 * Check for SQL injection vulnerabilities
 */
function checkSQLInjection() {
  log('INFO', 'Checking for potential SQL injection vulnerabilities...');
  
  const sqlFiles = ['sql/*.sql', 'src/**/*.ts', 'src/**/*.js'];
  
  // This is a simplified check - in practice, you'd use more sophisticated tools
  const dangerousPatterns = [
    { pattern: /\$\{.*\}/, name: 'Template literal in SQL' },
    { pattern: /\+.*req\./, name: 'String concatenation with request data' },
    { pattern: /query.*\+/, name: 'Dynamic query construction' }
  ];
  
  // Note: This would need to be expanded with proper file globbing
  log('INFO', 'SQL injection check requires manual review of dynamic queries');
}

/**
 * Generate security report
 */
function generateReport() {
  console.log(`\n${colors.bold}=== SECURITY AUDIT REPORT ===${colors.reset}\n`);
  
  const totalIssues = Object.values(auditResults).reduce((sum, arr) => sum + arr.length, 0);
  
  console.log(`${colors.bold}Summary:${colors.reset}`);
  console.log(`  Critical Issues: ${colors.red}${auditResults.critical.length}${colors.reset}`);
  console.log(`  High Priority:   ${colors.yellow}${auditResults.high.length}${colors.reset}`);
  console.log(`  Medium Priority: ${colors.blue}${auditResults.medium.length}${colors.reset}`);
  console.log(`  Low Priority:    ${colors.green}${auditResults.low.length}${colors.reset}`);
  console.log(`  Total Issues:    ${totalIssues}\n`);
  
  // Risk assessment
  let riskLevel = 'LOW';
  if (auditResults.critical.length > 0) {
    riskLevel = 'CRITICAL';
  } else if (auditResults.high.length > 0) {
    riskLevel = 'HIGH';
  } else if (auditResults.medium.length > 2) {
    riskLevel = 'MEDIUM';
  }
  
  console.log(`${colors.bold}Overall Risk Level: ${colors.red}${riskLevel}${colors.reset}\n`);
  
  // Recommendations
  console.log(`${colors.bold}Immediate Actions Required:${colors.reset}`);
  if (auditResults.critical.length > 0) {
    console.log(`  🚨 ${colors.red}URGENT: Address ${auditResults.critical.length} critical vulnerabilities immediately${colors.reset}`);
  }
  if (auditResults.high.length > 0) {
    console.log(`  ⚠️  ${colors.yellow}HIGH: Fix ${auditResults.high.length} high-priority issues within 24 hours${colors.reset}`);
  }
  if (auditResults.medium.length > 0) {
    console.log(`  📋 ${colors.blue}MEDIUM: Address ${auditResults.medium.length} medium-priority issues within 1 week${colors.reset}`);
  }
  
  // Save detailed report
  const reportPath = path.join(projectRoot, 'security-audit-results.json');
  fs.writeFileSync(reportPath, JSON.stringify(auditResults, null, 2));
  console.log(`\n${colors.green}Detailed report saved to: ${reportPath}${colors.reset}`);
}

/**
 * Main audit function
 */
function runSecurityAudit() {
  console.log(`${colors.bold}🔒 Starting Comprehensive Security Audit...${colors.reset}\n`);
  
  try {
    checkExposedSecrets();
    checkDebugRoutes();
    checkAPIEndpoints();
    checkEnvironmentVariables();
    checkSecurityHeaders();
    checkSQLInjection();
    
    generateReport();
    
  } catch (error) {
    console.error(`${colors.red}Error during security audit: ${error.message}${colors.reset}`);
    process.exit(1);
  }
}

// Run the audit
if (import.meta.url === `file://${process.argv[1]}`) {
  runSecurityAudit();
}

export { runSecurityAudit, auditResults };
