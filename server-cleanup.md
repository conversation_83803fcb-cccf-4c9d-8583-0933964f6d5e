# Server Cleanup Documentation

## Overview

This document outlines the cleanup of deprecated servers in the Class Tasker Connect project. The cleanup was performed to streamline the project, reduce confusion, and improve maintainability.

## Changes Made

### 1. Removed Deprecated Servers

The following server files were removed:

- `server/mock-stripe-server.js`: This was a mock implementation of the Stripe API used during development. It has been replaced by the real Stripe API server running in test mode.
- `server/https-server.js`: This was an unused HTTPS server implementation.

### 2. Updated npm Scripts

The following npm scripts were updated:

- Removed `mock-stripe-server` script
- Removed `admin-server` script
- Updated `dev:all` script to only start the necessary servers:
  ```json
  "dev:all": "concurrently \"npm run dev\" \"npm run stripe-server\""
  ```

### 3. Created Backups

Before removing the files, backups were created in the `server/backup` directory:

- `server/backup/mock-stripe-server.js`
- `server/backup/https-server.js`

## Current Server Architecture

The project now uses the following servers:

1. **Stripe API Server** (`server/index.js`):
   - Handles all Stripe-related functionality
   - Runs on port 3001
   - Started with `npm run stripe-server`

2. **Development Server** (Vite):
   - Serves the frontend application
   - Runs on port 8082
   - Started with `npm run dev`

3. **Supabase Edge Functions**:
   - Handles email functionality and other serverless operations
   - Hosted on Supabase.com
   - No local server needed

## Starting the Development Environment

To start the complete development environment, use:

```bash
npm run dev:all
```

This will start both the frontend development server and the Stripe API server.

## Notes

- Email functionality has been migrated from the Express server to Supabase Edge Functions.
- The mock Stripe server is no longer needed as the real Stripe API server can be used in test mode.
- The admin server functionality has been migrated to Supabase Edge Functions.
