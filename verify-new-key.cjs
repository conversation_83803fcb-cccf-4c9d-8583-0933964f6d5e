// Script to verify the new Stripe API key
require('dotenv').config();
const Stripe = require('stripe');

// Get the API key from the environment
const apiKey = process.env.STRIPE_SECRET_KEY;

console.log('API key from environment:', apiKey);
console.log('API key length:', apiKey.length);

// Try to use the API key
try {
  const stripe = new Stripe(apiKey, {
    apiVersion: '2023-10-16',
  });

  // Make a simple API call
  stripe.account.retrieve()
    .then(account => {
      console.log('API key is valid!');
      console.log('Account ID:', account.id);
      console.log('Account email:', account.email);
      
      // List recent invoices
      return stripe.invoices.list({ limit: 5 });
    })
    .then(invoices => {
      console.log(`Found ${invoices.data.length} invoices:`);
      invoices.data.forEach((invoice, index) => {
        console.log(`\nInvoice ${index + 1}:`);
        console.log(`- ID: ${invoice.id}`);
        console.log(`- Customer: ${invoice.customer}`);
        console.log(`- Customer Email: ${invoice.customer_email}`);
        console.log(`- Status: ${invoice.status}`);
        console.log(`- Total: ${invoice.total / 100} ${invoice.currency.toUpperCase()}`);
        console.log(`- Created: ${new Date(invoice.created * 1000).toISOString()}`);
      });
    })
    .catch(error => {
      console.error('Error using API key:', error.message);
    });
} catch (error) {
  console.error('Error initializing Stripe:', error.message);
}
