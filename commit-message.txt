Server Cleanup: Remove deprecated servers and update scripts

- Removed mock-stripe-server.js (replaced by real Stripe API server in test mode)
- Removed https-server.js (unused)
- Removed admin-server script (functionality migrated to Supabase Edge Functions)
- Updated dev:all script to only start necessary servers
- Created backups of removed files in server/backup directory
- Added server-cleanup.md documentation
- Updated README.md with current server architecture

This cleanup streamlines the project, reduces confusion, and improves maintainability by removing unused code and clarifying the server architecture.
