<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Security Edge Function Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Security Edge Function Test</h1>
        <p>This tool tests the security-alert Edge Function directly.</p>

        <div class="test-section">
            <h3>📋 Configuration</h3>
            <p><strong>Project URL:</strong> https://qcnotlojmyvpqbbgoxbc.supabase.co</p>
            <p><strong>Function URL:</strong> https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/security-alert</p>
            <p><strong>Status:</strong> <span id="status">Not tested</span></p>
        </div>

        <div class="test-section">
            <h3>🧪 Test Functions</h3>
            <button onclick="testSupabaseDashboard()">Test Supabase Dashboard Format</button>
            <button onclick="testCriticalAlert()">Test Critical Alert</button>
            <button onclick="testAuthFailure()">Test Auth Failure</button>
            <button onclick="testSQLInjection()">Test SQL Injection</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>

        <div class="test-section">
            <h3>📊 Test Results</h3>
            <div id="logs" class="log">Click a test button to start testing...</div>
        </div>
    </div>

    <script>
        const SUPABASE_URL = 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
        const SUPABASE_ANON_KEY = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzE2NzU2NzQsImV4cCI6MjA0NzI1MTY3NH0.Hs_4vKJQOP_TJKhZJGMKJKhZJGMKJKhZJGMKJKhZJGM';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : '';
            logElement.innerHTML += `<div class="${className}">[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = 'Logs cleared...';
        }

        async function callEdgeFunction(eventData) {
            try {
                log(`Sending request to Edge Function...`);
                log(`Event data: ${JSON.stringify(eventData, null, 2)}`);

                const response = await fetch(`${SUPABASE_URL}/functions/v1/security-alert`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SUPABASE_ANON_KEY}`,
                        'apikey': SUPABASE_ANON_KEY
                    },
                    body: JSON.stringify(eventData)
                });

                log(`Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.text();
                log(`Response body: ${responseData}`);

                if (response.ok) {
                    log('✅ Edge Function call successful!', 'success');
                    document.getElementById('status').innerHTML = '<span class="success">✅ Working</span>';
                } else {
                    log('❌ Edge Function call failed!', 'error');
                    document.getElementById('status').innerHTML = '<span class="error">❌ Error</span>';
                }

                return response.ok;
            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                document.getElementById('status').innerHTML = '<span class="error">❌ Network Error</span>';
                return false;
            }
        }

        async function testSupabaseDashboard() {
            log('🔧 Testing Supabase Dashboard Format...');
            log('This simulates what Supabase Dashboard sends when you click "Test"');

            const eventData = {
                name: "Functions"
            };

            await callEdgeFunction(eventData);
        }

        async function testCriticalAlert() {
            log('🚨 Testing Critical Security Alert...');

            const eventData = {
                type: 'suspicious_activity',
                severity: 'critical',
                userEmail: '<EMAIL>',
                action: 'manual_security_test',
                resource: 'edge_function_test',
                details: {
                    message: 'Manual Edge Function test',
                    testType: 'critical_alert',
                    source: 'test_page'
                },
                ipAddress: '127.0.0.1',
                userAgent: 'Edge Function Test',
                timestamp: new Date().toISOString()
            };

            await callEdgeFunction(eventData);
        }

        async function testAuthFailure() {
            log('🔐 Testing Authentication Failure...');

            const eventData = {
                type: 'auth_failure',
                severity: 'high',
                userEmail: '<EMAIL>',
                action: 'multiple_failed_login_attempts',
                resource: 'authentication_system',
                details: {
                    attempts: 15,
                    timeWindow: '5 minutes',
                    blocked: true
                },
                ipAddress: '*************',
                userAgent: 'Automated Attack Tool',
                timestamp: new Date().toISOString()
            };

            await callEdgeFunction(eventData);
        }

        async function testSQLInjection() {
            log('⚠️ Testing SQL Injection Detection...');

            const eventData = {
                type: 'suspicious_activity',
                severity: 'critical',
                userEmail: 'unknown',
                action: 'sql_injection_attempt',
                resource: 'user_database',
                details: {
                    query: "'; DROP TABLE users; --",
                    blocked: true,
                    source: 'contact_form',
                    detectionMethod: 'pattern_matching'
                },
                ipAddress: '************',
                userAgent: 'Mozilla/5.0 (Malicious Bot)',
                timestamp: new Date().toISOString()
            };

            await callEdgeFunction(eventData);
        }

        // Test on page load
        window.onload = function() {
            log('🛡️ Security Edge Function Test Tool Loaded');
            log('Ready to test the security-alert Edge Function');
            log('Make sure the Edge Function is deployed in Supabase Dashboard');
        };
    </script>
</body>
</html>
