# PowerShell script to find all hardcoded API keys in the repository

Write-Host "🔍 Scanning for hardcoded API keys..." -ForegroundColor Yellow

# Define the API keys to search for
$apiKeys = @(
    "qeaycbch5vhf",
    "e379cn39jrwz",
    "AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58",
    "pk_test_51REKxHAwo0W7IrjowNl7nykyo6V6RwzMuo0aTJk2gHueU3nTC5WiJaTmGZ648ZndgM1WoR675qFU90f774bPIqCL00xGP85S6u",
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InFjbm90bG9qbXl2cHFiYmdveGJjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ3NTg4ODQsImV4cCI6MjA2MDMzNDg4NH0.8kJRzyXPp-nW0PHOYFFk9SHPP9Ts8UnXapMkIZWv4iY"
)

# Define directories to exclude
$excludeDirs = @("node_modules", ".git", "dist", "build")

# Get all files recursively, excluding certain directories
$files = Get-ChildItem -Recurse -File | Where-Object {
    $file = $_
    $exclude = $false
    foreach ($dir in $excludeDirs) {
        if ($file.FullName -like "*\$dir\*") {
            $exclude = $true
            break
        }
    }
    -not $exclude
}

$foundKeys = @()

Write-Host "Checking $($files.Count) files..." -ForegroundColor Blue

foreach ($file in $files) {
    try {
        $content = Get-Content $file.FullName -Raw -ErrorAction SilentlyContinue
        if ($content) {
            foreach ($key in $apiKeys) {
                if ($content -like "*$key*") {
                    $foundKeys += [PSCustomObject]@{
                        File = $file.FullName.Replace((Get-Location).Path + "\", "")
                        Key = $key
                        KeyType = switch ($key) {
                            "qeaycbch5vhf" { "GetStream API Key (Current)" }
                            "e379cn39jrwz" { "GetStream API Key (Old)" }
                            "AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58" { "Google Maps API Key" }
                            {$_ -like "pk_test_*"} { "Stripe Public Key" }
                            {$_ -like "eyJ*"} { "Supabase Anon Key" }
                            default { "Unknown Key" }
                        }
                    }
                    Write-Host "🚨 FOUND: $($key.Substring(0, [Math]::Min(20, $key.Length)))... in $($file.Name)" -ForegroundColor Red
                }
            }
        }
    }
    catch {
        # Skip binary files or files that can't be read
    }
}

Write-Host "`n📊 SCAN RESULTS:" -ForegroundColor Yellow
Write-Host "=================" -ForegroundColor Yellow

if ($foundKeys.Count -eq 0) {
    Write-Host "✅ No hardcoded API keys found!" -ForegroundColor Green
} else {
    Write-Host "🚨 Found $($foundKeys.Count) hardcoded API key(s):" -ForegroundColor Red

    $foundKeys | Group-Object KeyType | ForEach-Object {
        Write-Host "`n$($_.Name):" -ForegroundColor Yellow
        $_.Group | ForEach-Object {
            Write-Host "  📁 $($_.File)" -ForegroundColor White
            Write-Host "     🔑 $($_.Key.Substring(0, [Math]::Min(30, $_.Key.Length)))..." -ForegroundColor Gray
        }
    }

    Write-Host "`n⚠️  SECURITY ALERT: These keys should be rotated immediately!" -ForegroundColor Red
    Write-Host "🔧 Run the security cleanup script to fix these issues." -ForegroundColor Yellow
}

Write-Host "`n🔍 Scan complete." -ForegroundColor Blue
