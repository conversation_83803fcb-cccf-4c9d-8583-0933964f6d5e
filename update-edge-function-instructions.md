# Instructions for Updating the Edge Function

Since we can't deploy directly through the CLI due to Docker requirements, follow these steps to update the Edge Function through the Supabase Dashboard:

## 1. Copy the Updated Edge Function Code

```typescript
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Stripe - prefer live key if available
    const liveKey = Deno.env.get('STRIPE_LIVE_SECRET_KEY');
    const testKey = Deno.env.get('STRIPE_SECRET_KEY');
    const apiKey = liveKey || testKey || '';
    const isLiveMode = !!liveKey;
    
    console.log(`Using Stripe in ${isLiveMode ? 'LIVE' : 'TEST'} mode`);
    
    const stripe = new Stripe(apiKey, {
      apiVersion: '2023-10-16',
    })

    // Get the invoice ID from the request
    const { invoiceId } = await req.json()

    if (!invoiceId) {
      return new Response(
        JSON.stringify({ error: 'Invoice ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Sending email for invoice ${invoiceId}`)

    try {
      // First, retrieve the invoice to check its details
      console.log(`Retrieving invoice ${invoiceId} before sending...`)
      const invoice = await stripe.invoices.retrieve(invoiceId)
      
      console.log('Invoice details:', {
        id: invoice.id,
        customer_email: invoice.customer_email,
        customer_id: invoice.customer,
        status: invoice.status,
        collection_method: invoice.collection_method,
        number: invoice.number
      })
      
      // Send the invoice email
      console.log(`Sending invoice ${invoiceId} email...`)
      const sentInvoice = await stripe.invoices.sendInvoice(invoiceId)
      
      console.log('Invoice email sent successfully:', {
        id: sentInvoice.id,
        status: sentInvoice.status,
        hosted_invoice_url: sentInvoice.hosted_invoice_url
      })

      // Prepare response message based on mode
      const responseMessage = isLiveMode
        ? "Invoice email sent successfully. Since we're in LIVE mode, an actual email has been sent to the customer's email address."
        : "Invoice email sent successfully. Note: In test mode, Stripe doesn't actually send emails to real addresses. Check the Stripe Dashboard Events section to see the attempted email.";
      
      return new Response(
        JSON.stringify({ 
          sent: true, 
          invoice: sentInvoice,
          message: responseMessage,
          mode: isLiveMode ? 'live' : 'test'
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (stripeError) {
      console.error('Stripe error sending invoice email:', stripeError)
      
      return new Response(
        JSON.stringify({ 
          error: 'Stripe error sending invoice email', 
          details: stripeError.message 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
  } catch (error) {
    console.error('Error sending invoice email:', error)
    
    return new Response(
      JSON.stringify({ error: 'Failed to send invoice email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
```

## 2. Update the Function in Supabase Dashboard

1. Go to the Supabase Dashboard: https://supabase.com/dashboard/project/qcnotlojmyvpqbbgoxbc/functions
2. Click on the existing `send-invoice-email` function
3. Click "Edit" to modify the function
4. Replace the entire code with the updated code above
5. Click "Save" to update the function

## 3. Set the Environment Variables

1. In the function settings, make sure you have both environment variables:
   - `STRIPE_SECRET_KEY`: Your test mode secret key
   - `STRIPE_LIVE_SECRET_KEY`: Your live mode secret key

## 4. Test the Function

1. Go back to your application
2. Try sending an invoice email
3. Check the toast message to see which mode (TEST or LIVE) is being used
4. If in LIVE mode, check your email for the invoice

## 5. Monitoring

1. Check the Supabase Edge Function logs for any errors
2. Check the Stripe Dashboard Events for email events
