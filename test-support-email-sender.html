<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Support Email Sender Function</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #0056b3; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
        .log { max-height: 400px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🧪 Test Support Email Sender Function</h1>
    <p>This tests the support-email-sender Edge Function that invitations actually use.</p>

    <div class="test-section">
        <h3>Test 1: Basic Support Email</h3>
        <button onclick="testBasicSupport()">Test Basic Support Email</button>
        <div id="basic-result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Security Alert Format</h3>
        <button onclick="testSecurityAlert()">Test Security Alert Email</button>
        <div id="security-result"></div>
    </div>

    <div class="test-section">
        <h3>📋 Test Logs</h3>
        <button onclick="clearLogs()">Clear Logs</button>
        <div id="logs" class="log"></div>
    </div>

    <script>
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '';
        }

        async function testBasicSupport() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = '<p>Testing basic support email...</p>';

            try {
                log('🧪 Testing basic support email with support-email-sender function');

                const emailRequest = {
                    from: 'ClassTasker Test <<EMAIL>>',
                    to: '<EMAIL>',
                    subject: 'Test Support Email from ClassTasker',
                    name: 'Test User',
                    email: '<EMAIL>',
                    support_type: 'Test',
                    message: 'This is a test email from the support-email-sender function. If you receive this, the function is working correctly!'
                };

                log('📤 Sending request to support-email-sender function...');
                log('Request payload: ' + JSON.stringify(emailRequest, null, 2));

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/support-email-sender', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                        // No Authorization header - testing without authentication
                    },
                    body: JSON.stringify(emailRequest)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Support email sent successfully!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Basic support email test PASSED');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Basic support email test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 Basic support email test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        async function testSecurityAlert() {
            const resultDiv = document.getElementById('security-result');
            resultDiv.innerHTML = '<p>Testing security alert email...</p>';

            try {
                log('🚨 Testing security alert email format');

                const securityEmailHtml = `
                    <h2>Critical Security Alert</h2>
                    <p>A critical security event has been detected on ClassTasker:</p>

                    <table style="border-collapse: collapse; width: 100%; margin: 20px 0;">
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Event Type:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">suspicious_activity</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Severity:</td>
                            <td style="padding: 10px; border: 1px solid #ddd; color: red; font-weight: bold;">CRITICAL</td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Action:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">Test security alert</td>
                        </tr>
                        <tr>
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">User:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;"><EMAIL></td>
                        </tr>
                        <tr style="background-color: #f8f9fa;">
                            <td style="padding: 10px; border: 1px solid #ddd; font-weight: bold;">Timestamp:</td>
                            <td style="padding: 10px; border: 1px solid #ddd;">${new Date().toLocaleString()}</td>
                        </tr>
                    </table>

                    <p><strong>Immediate Action Required:</strong></p>
                    <ul>
                        <li>Review the security dashboard immediately</li>
                        <li>Investigate the user account if applicable</li>
                        <li>Check for any related suspicious activities</li>
                    </ul>

                    <p>Access the security dashboard: <a href="https://classtasker.com/admin/security">https://classtasker.com/admin/security</a></p>
                `;

                const emailRequest = {
                    from: 'ClassTasker Security <<EMAIL>>',
                    to: '<EMAIL>',
                    subject: '🚨 CRITICAL Security Alert - Test Alert',
                    name: 'ClassTasker Security System',
                    email: '<EMAIL>',
                    support_type: 'Security Alert',
                    message: 'Critical security event detected: Test security alert',
                    html_content: securityEmailHtml
                };

                log('📤 Sending security alert email...');
                log('Request payload: ' + JSON.stringify(emailRequest, null, 2));

                const response = await fetch('https://qcnotlojmyvpqbbgoxbc.supabase.co/functions/v1/support-email-sender', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                        // No Authorization header - testing without authentication
                    },
                    body: JSON.stringify(emailRequest)
                });

                log(`📨 Response status: ${response.status} ${response.statusText}`);

                const responseData = await response.json();
                log('📨 Response data: ' + JSON.stringify(responseData, null, 2));

                if (response.ok) {
                    resultDiv.innerHTML = `
                        <div class="success">
                            <h4>✅ Success!</h4>
                            <p>Security alert email sent successfully!</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('✅ Security alert email test PASSED');
                } else {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <h4>❌ Failed!</h4>
                            <p>Status: ${response.status}</p>
                            <pre>${JSON.stringify(responseData, null, 2)}</pre>
                        </div>
                    `;
                    log('❌ Security alert email test FAILED: ' + JSON.stringify(responseData));
                }

            } catch (error) {
                log('💥 Security alert email test ERROR: ' + error.message);
                resultDiv.innerHTML = `
                    <div class="error">
                        <h4>💥 Error!</h4>
                        <p>${error.message}</p>
                    </div>
                `;
            }
        }

        // Auto-clear logs on page load
        window.onload = function() {
            log('🚀 Support Email Sender Test Page Loaded');
            log('Ready to test the support-email-sender Edge Function');
        };
    </script>
</body>
</html>
