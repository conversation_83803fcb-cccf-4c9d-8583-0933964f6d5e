import fetch from 'node-fetch';
import dotenv from 'dotenv';

dotenv.config();

// SECURITY: Supabase API details - NEVER hardcode service role keys
const supabaseUrl = process.env.VITE_SUPABASE_URL || 'https://qcnotlojmyvpqbbgoxbc.supabase.co';
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

// Validate that the service role key is present
if (!supabaseKey) {
  console.error('SECURITY ERROR: SUPABASE_SERVICE_ROLE_KEY not found in environment variables');
  console.error('This script requires the service role key to function');
  process.exit(1);
}

// Users to create
const usersToCreate = [
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'maintenance',
    first_name: 'Maintenance',
    last_name: 'Staff',
    account_type: 'school'
  },
  {
    email: '<EMAIL>',
    password: 'test-password-123',
    role: 'support',
    first_name: 'Support',
    last_name: 'Staff',
    account_type: 'school'
  }
];

// Function to create a user via the Supabase API
async function createUser(userData) {
  try {
    console.log(`Creating user ${userData.email}...`);

    // Create user in auth
    const authResponse = await fetch(`${supabaseUrl}/auth/v1/admin/users`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      },
      body: JSON.stringify({
        email: userData.email,
        password: userData.password,
        email_confirm: true,
        user_metadata: {
          first_name: userData.first_name,
          last_name: userData.last_name,
          role: userData.role
        }
      })
    });

    if (!authResponse.ok) {
      const errorData = await authResponse.json();
      throw new Error(`Failed to create user: ${JSON.stringify(errorData)}`);
    }

    const authData = await authResponse.json();
    console.log(`User created with ID: ${authData.id}`);

    // Get organization ID
    const orgResponse = await fetch(`${supabaseUrl}/rest/v1/organizations?select=id&limit=1`, {
      headers: {
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`
      }
    });

    if (!orgResponse.ok) {
      throw new Error('Failed to fetch organization');
    }

    const organizations = await orgResponse.json();
    const organizationId = organizations.length > 0 ? organizations[0].id : null;

    if (!organizationId) {
      throw new Error('No organization found');
    }

    console.log(`Using organization ID: ${organizationId}`);

    // Create profile
    const profileResponse = await fetch(`${supabaseUrl}/rest/v1/profiles`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseKey,
        'Authorization': `Bearer ${supabaseKey}`,
        'Prefer': 'return=representation'
      },
      body: JSON.stringify({
        id: authData.id,
        email: [userData.email],
        role: userData.role,
        first_name: userData.first_name,
        last_name: userData.last_name,
        organization_id: organizationId,
        account_type: userData.account_type || 'school',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
    });

    if (!profileResponse.ok) {
      const errorData = await profileResponse.json();
      throw new Error(`Failed to create profile: ${JSON.stringify(errorData)}`);
    }

    const profileData = await profileResponse.json();
    console.log(`Profile created for ${userData.email}`);

    return { success: true, user: authData, profile: profileData };
  } catch (error) {
    console.error(`Error creating user ${userData.email}:`, error.message);
    return { success: false, error: error.message };
  }
}

// Main function to create all users
async function createAllUsers() {
  console.log('Starting user creation...');

  for (const userData of usersToCreate) {
    const result = await createUser(userData);
    if (result.success) {
      console.log(`Successfully created user and profile for ${userData.email}`);
    } else {
      console.log(`Failed to create user ${userData.email}`);
    }
    console.log('-----------------------------------');
  }

  console.log('User creation process completed');
}

// Run the function
createAllUsers();
