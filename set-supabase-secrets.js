// This script sets the necessary secrets for Supabase Edge Functions
// Run this script with: node set-supabase-secrets.js

const { execSync } = require('child_process');
require('dotenv').config();

// Check if the required environment variables are set
if (!process.env.STRIPE_SECRET_KEY) {
  console.error('Error: STRIPE_SECRET_KEY environment variable is not set.');
  process.exit(1);
}

console.log('Setting Supabase secrets for Edge Functions...');

try {
  // Set the Stripe secret key
  execSync(`supabase secrets set STRIPE_SECRET_KEY="${process.env.STRIPE_SECRET_KEY}"`, {
    stdio: 'inherit'
  });
  
  console.log('Successfully set STRIPE_SECRET_KEY secret.');
  
  // Set other secrets if needed
  // For example, if you need to set the Stripe webhook secret:
  if (process.env.STRIPE_WEBHOOK_SECRET) {
    execSync(`supabase secrets set STRIPE_WEBHOOK_SECRET="${process.env.STRIPE_WEBHOOK_SECRET}"`, {
      stdio: 'inherit'
    });
    console.log('Successfully set STRIPE_WEBHOOK_SECRET secret.');
  }
  
  console.log('All secrets have been set successfully!');
} catch (error) {
  console.error('Error setting Supabase secrets:', error.message);
  process.exit(1);
}
