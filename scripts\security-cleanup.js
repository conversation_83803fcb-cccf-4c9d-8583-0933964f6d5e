#!/usr/bin/env node

/**
 * Security Cleanup Script
 * 
 * This script performs a comprehensive security cleanup of the repository
 * after API keys have been rotated.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const projectRoot = path.resolve(__dirname, '..');

// Colors for console output
const colors = {
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  green: '\x1b[32m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bold: '\x1b[1m'
};

function log(level, message) {
  const color = {
    ERROR: colors.red,
    WARNING: colors.yellow,
    SUCCESS: colors.green,
    INFO: colors.blue
  }[level] || colors.reset;
  
  console.log(`${color}[${level}]${colors.reset} ${message}`);
}

/**
 * Check current status of security files
 */
function checkSecurityStatus() {
  log('INFO', 'Checking current security status...');
  
  const checks = [
    {
      name: 'env-config.js has no hardcoded keys',
      file: 'public/env-config.js',
      check: (content) => !content.includes('"qeaycbch5vhf"') && !content.includes('"e379cn39jrwz"')
    },
    {
      name: 'Debug routes are protected',
      file: 'src/App.tsx',
      check: (content) => content.includes('process.env.NODE_ENV === \'development\'')
    },
    {
      name: 'Git hooks are installed',
      file: '.git/hooks/pre-commit',
      check: (content) => content.includes('SECURITY ALERT')
    },
    {
      name: 'Environment variables properly configured',
      file: 'scripts/generate-env-config.cjs',
      check: (content) => content.includes('process.env.VITE_GETSTREAM_API_KEY')
    }
  ];
  
  let allPassed = true;
  
  checks.forEach(check => {
    const filePath = path.join(projectRoot, check.file);
    
    if (!fs.existsSync(filePath)) {
      log('ERROR', `❌ ${check.name}: File not found (${check.file})`);
      allPassed = false;
      return;
    }
    
    const content = fs.readFileSync(filePath, 'utf8');
    
    if (check.check(content)) {
      log('SUCCESS', `✅ ${check.name}`);
    } else {
      log('ERROR', `❌ ${check.name}`);
      allPassed = false;
    }
  });
  
  return allPassed;
}

/**
 * Scan for any remaining hardcoded secrets
 */
function scanForSecrets() {
  log('INFO', 'Scanning for remaining hardcoded secrets...');
  
  const secretPatterns = [
    { pattern: /qeaycbch5vhf/g, name: 'Old GetStream API Key' },
    { pattern: /e379cn39jrwz/g, name: 'Older GetStream API Key' },
    { pattern: /AIzaSyADP5PrGRFA7BDgenK26HrU66VITsHWL58/g, name: 'Old Google Maps API Key' },
    { pattern: /pk_test_51REKxHAwo0W7IrjowNl7nykyo6V6RwzMuo0aTJk2gHueU3nTC5WiJaTmGZ648ZndgM1WoR675qFU90f774bPIqCL00xGP85S6u/g, name: 'Old Stripe Public Key' }
  ];
  
  const filesToCheck = [
    'public/env-config.js',
    'index.html',
    'vite.config.ts',
    'src/integrations/supabase/client.ts',
    'src/config/stripeConfig.ts'
  ];
  
  let secretsFound = false;
  
  filesToCheck.forEach(filePath => {
    const fullPath = path.join(projectRoot, filePath);
    
    if (!fs.existsSync(fullPath)) {
      return;
    }
    
    const content = fs.readFileSync(fullPath, 'utf8');
    
    secretPatterns.forEach(({ pattern, name }) => {
      const matches = content.match(pattern);
      if (matches) {
        log('ERROR', `🚨 Found ${name} in ${filePath}`);
        secretsFound = true;
      }
    });
  });
  
  if (!secretsFound) {
    log('SUCCESS', '✅ No hardcoded secrets found in checked files');
  }
  
  return !secretsFound;
}

/**
 * Verify environment variable setup
 */
function verifyEnvironmentSetup() {
  log('INFO', 'Verifying environment variable setup...');
  
  const envExamplePath = path.join(projectRoot, '.env.example');
  const envLocalPath = path.join(projectRoot, '.env.local');
  
  // Check .env.example exists and has proper structure
  if (!fs.existsSync(envExamplePath)) {
    log('WARNING', '.env.example file not found');
    return false;
  }
  
  const envExample = fs.readFileSync(envExamplePath, 'utf8');
  const requiredVars = [
    'VITE_GETSTREAM_API_KEY',
    'VITE_SUPABASE_URL',
    'VITE_SUPABASE_ANON_KEY',
    'VITE_STRIPE_PUBLIC_KEY',
    'VITE_GOOGLE_MAPS_API_KEY'
  ];
  
  let allVarsPresent = true;
  requiredVars.forEach(varName => {
    if (!envExample.includes(varName)) {
      log('ERROR', `Missing ${varName} in .env.example`);
      allVarsPresent = false;
    }
  });
  
  if (allVarsPresent) {
    log('SUCCESS', '✅ .env.example has all required variables');
  }
  
  // Check if .env.local exists (it should for development)
  if (fs.existsSync(envLocalPath)) {
    log('INFO', '✅ .env.local exists (good for development)');
  } else {
    log('WARNING', '.env.local not found - create it for local development');
  }
  
  return allVarsPresent;
}

/**
 * Generate security report
 */
function generateSecurityReport() {
  log('INFO', 'Generating security report...');
  
  const report = {
    timestamp: new Date().toISOString(),
    status: 'SECURE',
    checks: {
      hardcodedSecrets: scanForSecrets(),
      securityFiles: checkSecurityStatus(),
      environmentSetup: verifyEnvironmentSetup()
    },
    recommendations: []
  };
  
  // Determine overall status
  const allChecksPassed = Object.values(report.checks).every(check => check === true);
  
  if (!allChecksPassed) {
    report.status = 'NEEDS_ATTENTION';
  }
  
  // Add recommendations
  if (!report.checks.hardcodedSecrets) {
    report.recommendations.push('Remove remaining hardcoded secrets from files');
  }
  
  if (!report.checks.securityFiles) {
    report.recommendations.push('Fix security configuration issues');
  }
  
  if (!report.checks.environmentSetup) {
    report.recommendations.push('Complete environment variable setup');
  }
  
  // Save report
  const reportPath = path.join(projectRoot, 'security-cleanup-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  log('SUCCESS', `Security report saved to: security-cleanup-report.json`);
  
  return report;
}

/**
 * Main cleanup function
 */
function runSecurityCleanup() {
  console.log(`${colors.bold}🔒 Security Cleanup Script${colors.reset}\n`);
  
  log('INFO', 'Starting comprehensive security cleanup...');
  
  // Run all checks
  const report = generateSecurityReport();
  
  console.log(`\n${colors.bold}=== SECURITY CLEANUP SUMMARY ===${colors.reset}\n`);
  
  if (report.status === 'SECURE') {
    console.log(`${colors.green}✅ SECURITY STATUS: SECURE${colors.reset}`);
    console.log(`${colors.green}All security checks passed!${colors.reset}`);
  } else {
    console.log(`${colors.yellow}⚠️  SECURITY STATUS: NEEDS ATTENTION${colors.reset}`);
    console.log(`${colors.yellow}Some issues need to be addressed:${colors.reset}`);
    
    report.recommendations.forEach(rec => {
      console.log(`${colors.yellow}  • ${rec}${colors.reset}`);
    });
  }
  
  console.log(`\n${colors.bold}Next Steps:${colors.reset}`);
  console.log(`${colors.blue}1. Install git hooks: node scripts/install-git-hooks.js${colors.reset}`);
  console.log(`${colors.blue}2. Test the setup: npm run build${colors.reset}`);
  console.log(`${colors.blue}3. Commit changes: git add . && git commit -m "SECURITY: Complete cleanup"${colors.reset}`);
  console.log(`${colors.blue}4. Deploy to production with new environment variables${colors.reset}`);
  
  if (report.status !== 'SECURE') {
    console.log(`\n${colors.red}⚠️  Address the issues above before deploying to production${colors.reset}`);
    process.exit(1);
  }
}

// Run the cleanup
if (import.meta.url === `file://${process.argv[1]}`) {
  runSecurityCleanup();
}

export { runSecurityCleanup, checkSecurityStatus, scanForSecrets };
