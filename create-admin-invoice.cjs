// Script to create a test <NAME_EMAIL>
require('dotenv').config();
const Stripe = require('stripe');
const { createClient } = require('@supabase/supabase-js');

// Initialize Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createAdminInvoice() {
  try {
    console.log('Creating a test <NAME_EMAIL>...');
    console.log(`Using API key: ${stripeSecretKey.substring(0, 7)}...`);
    console.log(`API mode: ${stripeSecretKey.startsWith('sk_live_') ? 'LIVE' : 'TEST'}`);

    // First, create a customer with the specific email
    const customer = await stripe.customers.create({
      email: '<EMAIL>',
      name: 'Admin ClassTasker',
      description: 'Test customer for invoice email testing (<EMAIL>)',
    });

    console.log(`Created customer: ${customer.id}`);

    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customer.id,
      amount: 2500, // $25.00
      currency: 'gbp',
      description: 'Test Invoice <NAME_EMAIL>',
    });

    console.log(`Created invoice item: ${invoiceItem.id}`);

    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      auto_advance: false, // Don't automatically finalize the invoice
      collection_method: 'send_invoice',
      days_until_due: 30,
    });

    console.log(`Created invoice: ${invoice.id}`);

    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);

    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice status: ${finalizedInvoice.status}`);
    console.log(`Invoice total: ${finalizedInvoice.total / 100} ${finalizedInvoice.currency.toUpperCase()}`);
    console.log(`Invoice URL: ${finalizedInvoice.hosted_invoice_url}`);

    // Now add this invoice to the database
    console.log('\nAdding invoice to database...');

    // First, check if we have any existing organizations
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      return;
    }

    if (!orgs || orgs.length === 0) {
      console.error('No organizations found. Please create an organization first.');
      return;
    }

    const organizationId = orgs[0].id;
    console.log(`Using organization ID: ${organizationId}`);

    // Get a user for the payment record
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .eq('organization_id', organizationId)
      .limit(1);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }

    if (!users || users.length === 0) {
      console.error('No users found in the organization. Please create a user first.');
      return;
    }

    const userId = users[0].id;
    console.log(`Using user ID: ${userId}`);

    // Check if we have any existing tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title')
      .limit(1);

    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
      return;
    }

    let taskId, taskTitle;
    if (!tasks || tasks.length === 0) {
      console.log('No tasks found. Creating a test task...');

      // Create a test task
      const { data: newTask, error: newTaskError } = await supabase
        .from('tasks')
        .insert({
          title: 'Test Task for Admin Invoice',
          description: 'This is a test <NAME_EMAIL> invoice testing',
          organization_id: organizationId,
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (newTaskError) {
        console.error('Error creating test task:', newTaskError);
        return;
      }

      taskId = newTask.id;
      taskTitle = newTask.title;
    } else {
      taskId = tasks[0].id;
      taskTitle = tasks[0].title;
    }

    console.log(`Using task: ${taskTitle} (${taskId})`);

    // Create an offer for the task
    const { data: offer, error: offerError } = await supabase
      .from('offers')
      .insert({
        task_id: taskId,
        user_id: userId,
        amount: 25.00,
        message: 'Test <NAME_EMAIL> invoice testing',
        status: 'accepted',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (offerError) {
      console.error('Error creating offer record:', offerError);
      return;
    }

    console.log(`Created offer record: ${offer.id}`);

    // Now create a payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        task_id: taskId,
        offer_id: offer.id,
        payer_id: userId,
        payee_id: userId,
        amount: 25.00,
        platform_fee: 5.00,
        supplier_amount: 20.00,
        status: 'paid',
        currency: 'gbp',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return;
    }

    console.log(`Created payment record: ${payment.id}`);

    // Create the invoice record
    const { data: dbInvoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        payment_id: payment.id,
        invoice_number: finalizedInvoice.number,
        invoice_url: finalizedInvoice.hosted_invoice_url,
        stripe_invoice_id: finalizedInvoice.id,
        status: 'paid',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (invoiceError) {
      console.error('Error creating invoice record:', invoiceError);
      return;
    }

    console.log('Successfully added invoice to database:');
    console.log(dbInvoice);

    console.log('\nUse this invoice ID for testing:');
    console.log(finalizedInvoice.id);

    return finalizedInvoice;
  } catch (error) {
    console.error('Error creating admin invoice:', error);
  }
}

createAdminInvoice();
