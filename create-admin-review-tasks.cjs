// Script to create tasks for admin review
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Email address for the teacher
const teacherEmail = '<EMAIL>';

// Function to get user ID by email
async function getUserIdByEmail(email) {
  const { data: authData } = await supabase.auth.admin.listUsers();
  const user = authData.users.find(u => u.email === email);
  return user ? user.id : null;
}

async function createAdminReviewTasks() {
  try {
    console.log('Creating tasks for admin review...');
    console.log(`Teacher email: ${teacherEmail}`);
    
    // Step 1: Get teacher ID from email
    console.log('\nStep 1: Get teacher ID from email');
    
    const teacherId = await getUserIdByEmail(teacherEmail);
    if (!teacherId) {
      console.error(`Teacher with email ${teacherEmail} not found.`);
      return;
    }
    
    console.log(`Teacher ID: ${teacherId}`);
    
    // Step 2: Get teacher details
    console.log('\nStep 2: Get teacher details');
    
    const { data: teacher, error: teacherError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', teacherId)
      .single();
    
    if (teacherError) {
      console.error('Error fetching teacher:', teacherError);
      return;
    }
    
    if (!teacher) {
      console.error(`Teacher with ID ${teacherId} not found.`);
      return;
    }
    
    console.log('Teacher details:');
    console.log(`- ID: ${teacher.id}`);
    console.log(`- Email: ${Array.isArray(teacher.email) ? teacher.email[0] : teacher.email}`);
    console.log(`- Role: ${teacher.role}`);
    console.log(`- Organization ID: ${teacher.organization_id}`);
    
    // Step 3: Create first admin review task
    console.log('\nStep 3: Create first admin review task');
    
    const { data: task1, error: task1Error } = await supabase
      .from('tasks')
      .insert({
        title: 'Install New Whiteboard',
        description: 'We need a new whiteboard installed in the mathematics classroom. The old one is damaged beyond repair.',
        location: 'Mathematics Classroom',
        category: 'Equipment',
        budget: 0, // Internal task, no budget
        due_date: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days from now
        user_id: teacherId,
        status: 'open',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        visibility: 'admin',
        is_public: false,
        payment_status: 'not_required',
        assigned_to: null
      })
      .select()
      .single();
    
    if (task1Error) {
      console.error('Error creating first task:', task1Error);
      return;
    }
    
    console.log(`Created first admin review task: ${task1.title} (${task1.id})`);
    
    // Step 4: Create second admin review task
    console.log('\nStep 4: Create second admin review task');
    
    const { data: task2, error: task2Error } = await supabase
      .from('tasks')
      .insert({
        title: 'Repair Air Conditioning Unit',
        description: 'The air conditioning unit in the computer lab is not working properly. It makes a loud noise and doesn\'t cool the room effectively.',
        location: 'Computer Lab',
        category: 'HVAC',
        budget: 0, // Internal task, no budget
        due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days from now
        user_id: teacherId,
        status: 'open',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        visibility: 'admin',
        is_public: false,
        payment_status: 'not_required',
        assigned_to: null
      })
      .select()
      .single();
    
    if (task2Error) {
      console.error('Error creating second task:', task2Error);
      return;
    }
    
    console.log(`Created second admin review task: ${task2.title} (${task2.id})`);
    
    console.log('\nAdmin review tasks created successfully!');
    console.log('Summary:');
    console.log(`1. ${task1.title} (${task1.id})`);
    console.log(`   - Due: ${new Date(task1.due_date).toLocaleDateString()}`);
    console.log(`   - Location: ${task1.location}`);
    console.log(`   - Category: ${task1.category}`);
    console.log(`   - Status: ${task1.status}`);
    console.log(`   - Visibility: ${task1.visibility}`);
    console.log(`2. ${task2.title} (${task2.id})`);
    console.log(`   - Due: ${new Date(task2.due_date).toLocaleDateString()}`);
    console.log(`   - Location: ${task2.location}`);
    console.log(`   - Category: ${task2.category}`);
    console.log(`   - Status: ${task2.status}`);
    console.log(`   - Visibility: ${task2.visibility}`);
    
    console.log('\nYou can now log in as an admin and assign these tasks through the dashboard.');
    console.log('Login URL: https://lovable.dev/login');
    console.log('Admin email: <EMAIL>');
    
    return {
      success: true,
      tasks: [task1, task2]
    };
  } catch (error) {
    console.error('Error creating admin review tasks:', error);
    return {
      success: false,
      error
    };
  }
}

createAdminReviewTasks();
