// Script to add the test invoice to the database
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The invoice ID from the previous step
const stripeInvoiceId = 'in_1RGIPaPWBcixtWOcVrHGQXkR';

async function addTestInvoiceToDb() {
  try {
    console.log('Adding test invoice to database...');

    // First, check if we have any existing organizations
    const { data: orgs, error: orgsError } = await supabase
      .from('organizations')
      .select('id')
      .limit(1);

    if (orgsError) {
      console.error('Error fetching organizations:', orgsError);
      return;
    }

    if (!orgs || orgs.length === 0) {
      console.error('No organizations found. Please create an organization first.');
      return;
    }

    const organizationId = orgs[0].id;
    console.log(`Using organization ID: ${organizationId}`);

    // Get a user for the payment record
    const { data: users, error: usersError } = await supabase
      .from('profiles')
      .select('id')
      .eq('organization_id', organizationId)
      .limit(1);

    if (usersError) {
      console.error('Error fetching users:', usersError);
      return;
    }

    if (!users || users.length === 0) {
      console.error('No users found in the organization. Please create a user first.');
      return;
    }

    const userId = users[0].id;
    console.log(`Using user ID: ${userId}`);

    // Check if we have any existing tasks
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title')
      .limit(1);

    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
      return;
    }

    let taskId, taskTitle;
    if (!tasks || tasks.length === 0) {
      console.log('No tasks found. Creating a test task...');

      // Create a test task
      const { data: newTask, error: newTaskError } = await supabase
        .from('tasks')
        .insert({
          title: 'Test Task for Invoice',
          description: 'This is a test task for invoice testing',
          organization_id: organizationId,
          status: 'completed',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        })
        .select()
        .single();

      if (newTaskError) {
        console.error('Error creating test task:', newTaskError);
        return;
      }

      taskId = newTask.id;
      taskTitle = newTask.title;
    } else {
      taskId = tasks[0].id;
      taskTitle = tasks[0].title;
    }

    console.log(`Using task: ${taskTitle} (${taskId})`);

    // Create an offer for the task
    const { data: offer, error: offerError } = await supabase
      .from('offers')
      .insert({
        task_id: taskId,
        user_id: userId,
        amount: 10.00,
        message: 'Test offer for invoice testing',
        status: 'accepted',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (offerError) {
      console.error('Error creating offer record:', offerError);
      return;
    }

    console.log(`Created offer record: ${offer.id}`);

    // Now create a payment record
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .insert({
        task_id: taskId,
        offer_id: offer.id,
        payer_id: userId, // The user is both payer and payee for this test
        payee_id: userId,
        amount: 10.00,
        platform_fee: 2.00,
        supplier_amount: 8.00,
        status: 'paid',
        currency: 'usd',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (paymentError) {
      console.error('Error creating payment record:', paymentError);
      return;
    }

    console.log(`Created payment record: ${payment.id}`);

    // Create the invoice record
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        payment_id: payment.id,
        // In a real scenario, we would get this from Stripe
        // For this test script, we're simulating a Stripe-generated invoice number
        invoice_number: `${stripeInvoiceId.substring(3, 11)}-0001`,
        invoice_url: 'https://invoice.stripe.com/i/acct_1RFz5yPWBcixtWOc/test_YWNjdF8xUkZ6NXlQV0JjaXh0V09jLF9TQWRnTEgydjFkREZOVnBwcUxEdmx1YW9kcTFicllqLDEzNTc3NTI3OQ0200UDQUg7Vl',
        stripe_invoice_id: stripeInvoiceId,
        status: 'paid',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .select()
      .single();

    if (invoiceError) {
      console.error('Error creating invoice record:', invoiceError);
      return;
    }

    console.log('Successfully added test invoice to database:');
    console.log(invoice);

    console.log('\nYou can now test sending an email for this invoice.');
  } catch (error) {
    console.error('Error adding test invoice to database:', error);
  }
}

addTestInvoiceToDb();
