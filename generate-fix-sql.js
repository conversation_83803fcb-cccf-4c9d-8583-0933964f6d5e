import fs from 'fs';
import path from 'path';

// Read the SQL file
const sqlFilePath = path.join(process.cwd(), 'sql', 'fix_notifications.sql');
const sqlQuery = fs.readFileSync(sqlFilePath, 'utf8');

console.log('=== SQL to Fix Notifications ===');
console.log(sqlQuery);
console.log('=== End of SQL ===');

// Create a test notification SQL
console.log('\n=== SQL to Create Test Notifications ===');
console.log(`
-- Create a test offer <NAME_EMAIL>
INSERT INTO public.notifications (
  user_id,
  type,
  message,
  related_id,
  related_type,
  read,
  email_sent
)
SELECT 
  p.id,
  'offer',
  'Test supplier has made an offer of £100.00 on your task "Test Task".',
  t.id,
  'offer',
  false,
  false
FROM 
  public.profiles p
  CROSS JOIN (
    SELECT id FROM public.tasks 
    WHERE user_id = (SELECT id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1)
    LIMIT 1
  ) t
WHERE 
  p.email = '<EMAIL>'
LIMIT 1;

-- Create a test message <NAME_EMAIL>
INSERT INTO public.notifications (
  user_id,
  type,
  message,
  related_id,
  related_type,
  read,
  email_sent
)
SELECT 
  p.id,
  'message',
  'You have received a new message from Test Supplier regarding "Test Task".',
  t.id,
  'message',
  false,
  false
FROM 
  public.profiles p
  CROSS JOIN (
    SELECT id FROM public.tasks 
    WHERE user_id = (SELECT id FROM public.profiles WHERE email = '<EMAIL>' LIMIT 1)
    LIMIT 1
  ) t
WHERE 
  p.email = '<EMAIL>'
LIMIT 1;
`);
console.log('=== End of SQL ===');
