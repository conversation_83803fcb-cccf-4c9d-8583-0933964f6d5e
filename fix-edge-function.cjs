// Script to fix the Supabase Edge Function for sending invoice emails
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function fixEdgeFunction() {
  try {
    console.log('Fixing Supabase Edge Function for sending invoice emails...');
    
    // Step 1: Check if the Stripe API key is working
    console.log('\nStep 1: Checking Stripe API key...');
    
    try {
      const balance = await stripe.balance.retrieve();
      console.log('Stripe API key is working!');
      console.log('Available balance:', balance.available.map(b => `${b.amount / 100} ${b.currency.toUpperCase()}`).join(', '));
    } catch (stripeError) {
      console.error('Error with Stripe API key:', stripeError.message);
      return;
    }
    
    // Step 2: Get a valid Stripe invoice ID from the database
    console.log('\nStep 2: Getting a valid Stripe invoice ID from the database...');
    
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .select('*')
      .not('stripe_invoice_id', 'is', null)
      .limit(1)
      .single();
    
    if (invoiceError) {
      console.error('Error fetching invoice:', invoiceError);
      return;
    }
    
    console.log(`Found invoice: ${invoice.id}`);
    console.log(`Stripe invoice ID: ${invoice.stripe_invoice_id}`);
    
    // Step 3: Try to send the invoice email directly using the Stripe API
    console.log('\nStep 3: Sending invoice email directly using Stripe API...');
    
    try {
      // First, retrieve the invoice to check its details
      console.log(`Retrieving invoice ${invoice.stripe_invoice_id}...`);
      const stripeInvoice = await stripe.invoices.retrieve(invoice.stripe_invoice_id);
      
      console.log('Invoice details:', {
        id: stripeInvoice.id,
        customer_email: stripeInvoice.customer_email,
        customer_id: stripeInvoice.customer,
        status: stripeInvoice.status,
        collection_method: stripeInvoice.collection_method,
        number: stripeInvoice.number
      });
      
      // Send the invoice email
      console.log(`Sending invoice ${invoice.stripe_invoice_id} email...`);
      const sentInvoice = await stripe.invoices.sendInvoice(invoice.stripe_invoice_id);
      
      console.log('Invoice email sent successfully:', {
        id: sentInvoice.id,
        status: sentInvoice.status,
        hosted_invoice_url: sentInvoice.hosted_invoice_url
      });
      
      console.log('\nDirect Stripe API test completed successfully!');
      console.log('This confirms that the Stripe API key is working correctly.');
      console.log('The issue is likely with the Supabase Edge Function configuration.');
      
      // Step 4: Update the Edge Function
      console.log('\nStep 4: Recommendations for fixing the Edge Function:');
      console.log('1. Make sure the STRIPE_SECRET_KEY environment variable is set in the Supabase dashboard');
      console.log('   - Go to https://supabase.com/dashboard/project/_/settings/api');
      console.log('   - Click on "Edge Functions" tab');
      console.log('   - Add STRIPE_SECRET_KEY with the value from your .env file');
      console.log('2. Redeploy the Edge Function using the Supabase CLI:');
      console.log('   - Run: npx supabase functions deploy send-invoice-email');
      console.log('3. Test the Edge Function using the Supabase CLI:');
      console.log('   - Run: npx supabase functions serve send-invoice-email');
      console.log('   - In another terminal, send a test request:');
      console.log(`   - curl -X POST http://localhost:54321/functions/v1/send-invoice-email -H "Content-Type: application/json" -d '{"invoiceId":"${invoice.stripe_invoice_id}"}'`);
    } catch (stripeError) {
      console.error('Error sending invoice email directly:', stripeError.message);
    }
  } catch (error) {
    console.error('Error fixing Edge Function:', error);
  }
}

fixEdgeFunction();
