const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Create a zip file of the Edge Function
function zipEdgeFunction() {
  const functionName = 'send-invoice-email';
  const functionDir = path.join(__dirname, 'supabase', 'functions', functionName);
  const outputZip = path.join(__dirname, `${functionName}.zip`);

  console.log(`Creating zip file for ${functionName} Edge Function...`);

  try {
    // Check if the function directory exists
    if (!fs.existsSync(functionDir)) {
      console.error(`Function directory not found: ${functionDir}`);
      process.exit(1);
    }

    // Create a zip file
    if (process.platform === 'win32') {
      // Windows
      execSync(`powershell Compress-Archive -Path "${functionDir}\\*" -DestinationPath "${outputZip}" -Force`);
    } else {
      // Linux/Mac
      execSync(`cd "${functionDir}" && zip -r "${outputZip}" .`);
    }

    console.log(`Successfully created zip file: ${outputZip}`);
    console.log('\nNext steps:');
    console.log('1. Go to Supabase Dashboard: https://supabase.com/dashboard/project/qcnotlojmyvpqbbgoxbc/functions');
    console.log('2. Click "New Function"');
    console.log('3. Enter name: send-invoice-email');
    console.log('4. Upload the zip file you just created');
    console.log('5. Set the STRIPE_SECRET_KEY environment variable');
    console.log('6. Deploy the function');
  } catch (error) {
    console.error('Error creating zip file:', error);
    process.exit(1);
  }
}

zipEdgeFunction();
