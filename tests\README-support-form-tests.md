# Support Form Tests

This directory contains test scripts for verifying the functionality of the support request form.

## Available Tests

1. **Browser Console Test** (`support-form-test.js`)
   - A script that can be run in the browser console to test the form UI
   - Tests form field population, validation, and submission

2. **API Test Script** (`../scripts/test-support-form-api.js`)
   - A Node.js script that tests the Supabase API directly
   - Tests database insertion with different authentication levels

3. **HTML Test Runner** (`support-form-test.html`)
   - A simple HTML page that helps run the browser console test
   - Provides a user-friendly interface for running tests

## Running the Tests

### Browser Console Test

1. Open the Help page at http://localhost:8082/help
2. Navigate to the Contact Support section
3. Open the browser developer tools (F12 or right-click > Inspect)
4. Go to the Console tab
5. Copy and paste the contents of `support-form-test.js` into the console
6. Press Enter to run the test

The test will automatically:
- Check if the form exists
- Verify if user information is pre-populated
- Fill in any missing required fields
- Submit the form
- Check if the success message appears

### API Test Script

1. Make sure you have Node.js installed
2. Create a `.env` file in the project root with the following variables:
   ```
   VITE_SUPABASE_URL=https://qcnotlojmyvpqbbgoxbc.supabase.co
   VITE_SUPABASE_ANON_KEY=your_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
   ```
3. Install dependencies if needed:
   ```
   npm install @supabase/supabase-js dotenv
   ```
4. Run the test script:
   ```
   node scripts/test-support-form-api.js
   ```

The script will:
- Test insertion with an anonymous client (should fail with proper RLS)
- Test insertion with an authenticated user (should succeed)
- Test insertion with a service role client (should succeed)
- Query and display recent support requests

### HTML Test Runner

1. Make sure the development server is running
2. Open `tests/support-form-test.html` in a browser
3. Follow the instructions on the page
4. Click the "Run Test" button

## Test Results

### Expected Results for Browser Console Test

If everything is working correctly, you should see:
- ✅ Form Exists: Support form found on the page
- ✅ Name Population: Name field is populated with: [Your Name]
- ✅ Email Population: Email field is populated with: [Your Email]
- ✅ Email Read-Only: Email field is correctly set to read-only for authenticated users
- ✅ Organization Population: Organization field is populated with: [Your Organization]
- ✅ Form Input: Set test message
- ✅ Form Submission: Form submitted successfully and success message displayed

### Expected Results for API Test

If everything is working correctly, you should see:
- ❌ Anonymous Insertion: Failed with error: [Error Message]
- ✅ Authentication: Successfully signed in
- ✅ Profile Fetch: Retrieved profile for [Your Name]
- ✅ Authenticated Insertion: Successfully inserted with ID: [ID]
- ✅ Service Role Insertion: Successfully inserted with ID: [ID]
- ✅ Query Requests: Retrieved [Number] support requests

## Troubleshooting

If the tests fail, check the following:

1. **Form Field Population Issues**
   - Verify that you're logged in
   - Check the AuthContext in the browser console
   - Verify that the profile data contains the expected fields

2. **Database Insertion Issues**
   - Check the RLS policies on the support_requests table
   - Verify that the authenticated user's email matches the email in the request
   - Check for any type mismatches in the data

3. **API Test Issues**
   - Verify that the environment variables are set correctly
   - Check that the test user credentials are valid
   - Ensure the Supabase service is running
