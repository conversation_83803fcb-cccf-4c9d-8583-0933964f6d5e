// Script to verify the test results
require('dotenv').config();
const Stripe = require('stripe');
const { createClient } = require('@supabase/supabase-js');

// Initialize Stripe
const stripeSecretKey = process.env.STRIPE_SECRET_KEY;

if (!stripeSecretKey) {
  console.error('Missing Stripe secret key. Check your environment variables.');
  process.exit(1);
}

const stripe = new Stripe(stripeSecretKey, {
  apiVersion: '2023-10-16',
});

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// The invoice ID from the test
const stripeInvoiceId = 'in_1RGJnBAwo0W7IrjoleWombXX';
const stripeCustomerId = 'cus_SAf7gGe5usmzQi';
const taskCreatorId = '4288cd97-e3ed-4e1d-8d22-abdc0d3f28bd';

async function verifyTestResults() {
  try {
    console.log('Verifying test results...');
    
    // Step 1: Verify the invoice in Stripe
    console.log('\nStep 1: Verify the invoice in Stripe');
    
    try {
      const invoice = await stripe.invoices.retrieve(stripeInvoiceId);
      
      console.log('Invoice details from Stripe:');
      console.log(`- ID: ${invoice.id}`);
      console.log(`- Number: ${invoice.number}`);
      console.log(`- Customer: ${invoice.customer}`);
      console.log(`- Customer Email: ${invoice.customer_email}`);
      console.log(`- Status: ${invoice.status}`);
      console.log(`- Total: ${invoice.total / 100} ${invoice.currency.toUpperCase()}`);
      console.log(`- Created: ${new Date(invoice.created * 1000).toISOString()}`);
      console.log(`- Hosted Invoice URL: ${invoice.hosted_invoice_url}`);
    } catch (error) {
      console.error('Error retrieving invoice from Stripe:', error.message);
    }
    
    // Step 2: Verify the customer in Stripe
    console.log('\nStep 2: Verify the customer in Stripe');
    
    try {
      const customer = await stripe.customers.retrieve(stripeCustomerId);
      
      console.log('Customer details from Stripe:');
      console.log(`- ID: ${customer.id}`);
      console.log(`- Email: ${customer.email}`);
      console.log(`- Name: ${customer.name}`);
      console.log(`- Description: ${customer.description}`);
      console.log(`- Created: ${new Date(customer.created * 1000).toISOString()}`);
      console.log(`- Metadata: ${JSON.stringify(customer.metadata)}`);
    } catch (error) {
      console.error('Error retrieving customer from Stripe:', error.message);
    }
    
    // Step 3: Verify the invoice in the database
    console.log('\nStep 3: Verify the invoice in the database');
    
    const { data: invoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .eq('stripe_invoice_id', stripeInvoiceId);
    
    if (invoicesError) {
      console.error('Error fetching invoices from database:', invoicesError);
    } else if (!invoices || invoices.length === 0) {
      console.log('No invoices found in the database with that Stripe invoice ID.');
    } else {
      console.log(`Found ${invoices.length} invoices in the database:`);
      invoices.forEach(invoice => {
        console.log('Invoice details from database:');
        console.log(`- ID: ${invoice.id}`);
        console.log(`- Invoice Number: ${invoice.invoice_number}`);
        console.log(`- Stripe Invoice ID: ${invoice.stripe_invoice_id}`);
        console.log(`- Status: ${invoice.status}`);
        console.log(`- Created At: ${invoice.created_at}`);
      });
    }
    
    // Step 4: Verify the customer in the database
    console.log('\nStep 4: Verify the customer in the database');
    
    try {
      const { data: customers, error: customersError } = await supabase
        .from('stripe_customers')
        .select('*')
        .eq('user_id', taskCreatorId);
      
      if (customersError) {
        console.error('Error fetching customers from database:', customersError);
      } else if (!customers || customers.length === 0) {
        console.log('No customers found in the database with that user ID.');
      } else {
        console.log(`Found ${customers.length} customers in the database:`);
        customers.forEach(customer => {
          console.log('Customer details from database:');
          console.log(`- ID: ${customer.id}`);
          console.log(`- User ID: ${customer.user_id}`);
          console.log(`- Customer ID: ${customer.customer_id}`);
          console.log(`- Created At: ${customer.created_at}`);
        });
      }
    } catch (error) {
      console.error('Error checking stripe_customers table:', error);
      console.log('The stripe_customers table might not exist.');
    }
    
    // Step 5: Verify the payment in the database
    console.log('\nStep 5: Verify the payment in the database');
    
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (paymentsError) {
      console.error('Error fetching payments from database:', paymentsError);
    } else if (!payments || payments.length === 0) {
      console.log('No payments found in the database.');
    } else {
      console.log('Latest payment details from database:');
      console.log(`- ID: ${payments[0].id}`);
      console.log(`- Task ID: ${payments[0].task_id}`);
      console.log(`- Offer ID: ${payments[0].offer_id}`);
      console.log(`- Payer ID: ${payments[0].payer_id}`);
      console.log(`- Payee ID: ${payments[0].payee_id}`);
      console.log(`- Amount: ${payments[0].amount} ${payments[0].currency}`);
      console.log(`- Status: ${payments[0].status}`);
      console.log(`- Created At: ${payments[0].created_at}`);
    }
  } catch (error) {
    console.error('Error verifying test results:', error);
  }
}

verifyTestResults();
