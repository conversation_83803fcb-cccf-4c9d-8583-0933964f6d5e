import { serve } from 'https://deno.land/std@0.168.0/http/server.ts'
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'
import Stripe from 'https://esm.sh/stripe@12.0.0'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the API key from environment variables
    const apiKey = Deno.env.get('STRIPE_SECRET_KEY');
    
    // Log the first few and last few characters of the key for debugging
    // (never log the entire key)
    if (apiKey) {
      const keyPrefix = apiKey.substring(0, 7);
      const keySuffix = apiKey.substring(apiKey.length - 4);
      const keyLength = apiKey.length;
      console.log(`Using Stripe API key: ${keyPrefix}...${keySuffix} (length: ${keyLength})`);
      console.log(`Key type: ${apiKey.startsWith('sk_live_') ? 'LIVE' : apiKey.startsWith('sk_test_') ? 'TEST' : 'UNKNOWN'}`);
    } else {
      console.error('No Stripe API key found in environment variables');
      return new Response(
        JSON.stringify({ error: 'Server configuration error: No API key available' }),
        { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
    
    const stripe = new Stripe(apiKey, {
      apiVersion: '2023-10-16',
    })

    // Get the invoice ID from the request
    const { invoiceId } = await req.json()

    if (!invoiceId) {
      return new Response(
        JSON.stringify({ error: 'Invoice ID is required' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }

    console.log(`Sending email for invoice ${invoiceId}`)

    try {
      // First, retrieve the invoice to check its details
      console.log(`Retrieving invoice ${invoiceId} before sending...`)
      const invoice = await stripe.invoices.retrieve(invoiceId)
      
      console.log('Invoice details:', {
        id: invoice.id,
        customer_email: invoice.customer_email,
        customer_id: invoice.customer,
        status: invoice.status,
        collection_method: invoice.collection_method,
        number: invoice.number
      })
      
      // Send the invoice email
      console.log(`Sending invoice ${invoiceId} email...`)
      const sentInvoice = await stripe.invoices.sendInvoice(invoiceId)
      
      console.log('Invoice email sent successfully:', {
        id: sentInvoice.id,
        status: sentInvoice.status,
        hosted_invoice_url: sentInvoice.hosted_invoice_url
      })

      // Prepare response message based on mode
      const isLiveMode = apiKey.startsWith('sk_live_');
      const responseMessage = isLiveMode
        ? "Invoice email sent successfully. Since we're in LIVE mode, an actual email has been sent to the customer's email address."
        : "Invoice email sent successfully. Note: In test mode, Stripe doesn't actually send emails to real addresses. Check the Stripe Dashboard Events section to see the attempted email.";
      
      return new Response(
        JSON.stringify({ 
          sent: true, 
          invoice: sentInvoice,
          message: responseMessage,
          mode: isLiveMode ? 'live' : 'test'
        }),
        { status: 200, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    } catch (stripeError) {
      console.error('Stripe error sending invoice email:', stripeError)
      
      return new Response(
        JSON.stringify({ 
          error: 'Stripe error sending invoice email', 
          details: stripeError.message 
        }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      )
    }
  } catch (error) {
    console.error('Error sending invoice email:', error)
    
    return new Response(
      JSON.stringify({ error: 'Failed to send invoice email' }),
      { status: 500, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )
  }
})
