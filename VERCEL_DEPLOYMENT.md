# Deploying to Vercel with GetStream Integration

This document provides instructions for deploying the application to Vercel with the GetStream chat integration.

## Prerequisites

1. A Vercel account
2. A GetStream account with API key and secret

## Setup Environment Variables

Before deploying, you need to set up the following environment variables in your Vercel project:

1. `GETSTREAM_API_KEY` - Your GetStream API key
2. `GETSTREAM_API_SECRET` - Your GetStream API secret

You can add these in the Vercel dashboard under Project Settings > Environment Variables.

## Update vercel.json

Make sure your vercel.json file includes the following configuration:

```json
{
  "rewrites": [
    { "source": "/api/(.*)", "destination": "/api/$1" },
    { "source": "/(.*)", "destination": "/index.html" }
  ],
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        { "key": "Access-Control-Allow-Credentials", "value": "true" },
        { "key": "Access-Control-Allow-Origin", "value": "*" },
        { "key": "Access-Control-Allow-Methods", "value": "GET,OPTIONS,PATCH,DELETE,POST,PUT" },
        { "key": "Access-Control-Allow-Headers", "value": "X-CSRF-Token, X-Requested-With, Accept, Accept-Version, Content-Length, Content-MD5, Content-Type, Date, X-Api-Version" }
      ]
    }
  ],
  "env": {
    "GETSTREAM_API_KEY": "@getstream-api-key",
    "GETSTREAM_API_SECRET": "@getstream-api-secret"
  }
}
```

## API Routes

The application uses the following API routes for GetStream integration:

1. `/api/getstream/token` - Generates tokens for GetStream users
2. `/api/getstream/channels` - Creates and manages GetStream channels
3. `/api/getstream/system-message` - Sends system messages to GetStream channels
4. `/api/getstream/members` - Adds members to GetStream channels

These routes are implemented as serverless functions that will be deployed automatically by Vercel.

## Deployment Steps

1. Push your code to GitHub
2. Connect your GitHub repository to Vercel
3. Configure the environment variables in the Vercel dashboard
4. Deploy the application

## Testing the Deployment

After deployment, test the following functionality:

1. User authentication and token generation
2. Creating new chat channels
3. Sending messages
4. Sending system messages
5. Adding members to channels

## Troubleshooting

If you encounter issues with the GetStream integration:

1. Check the Vercel function logs for errors
2. Verify that the environment variables are set correctly
3. Ensure that the API routes are being called with the correct parameters
4. Check the CORS configuration if you're experiencing cross-origin issues

## Additional Resources

- [Vercel Documentation](https://vercel.com/docs)
- [GetStream Documentation](https://getstream.io/chat/docs/)
- [Serverless Functions on Vercel](https://vercel.com/docs/serverless-functions/introduction)
