// Script to manually create an invoice for a task
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const stripe = require('stripe')(process.env.STRIPE_SECRET_KEY);

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createManualInvoice() {
  try {
    const taskId = 'fd946027-b773-43b4-b267-90a049b9458d';
    
    console.log(`Creating manual invoice for task: ${taskId}`);
    
    // Get task details
    const { data: task, error: taskError } = await supabase
      .from('tasks')
      .select('*, offers(*)')
      .eq('id', taskId)
      .single();
    
    if (taskError) {
      console.error('Error fetching task:', taskError);
      return;
    }
    
    console.log('Task Details:');
    console.log(`ID: ${task.id}`);
    console.log(`Title: ${task.title}`);
    console.log(`Status: ${task.status}`);
    console.log(`Payment Status: ${task.payment_status}`);
    
    // Get the accepted offer
    const acceptedOffer = task.offers.find(offer => offer.status === 'accepted');
    
    if (!acceptedOffer) {
      console.error('No accepted offer found for this task.');
      return;
    }
    
    console.log('\nAccepted Offer:');
    console.log(`ID: ${acceptedOffer.id}`);
    console.log(`Amount: £${acceptedOffer.amount}`);
    
    // Get the payment for this task
    const { data: payments, error: paymentsError } = await supabase
      .from('payments')
      .select('*')
      .eq('task_id', taskId)
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (paymentsError) {
      console.error('Error fetching payments:', paymentsError);
      return;
    }
    
    let payment;
    
    if (payments && payments.length > 0) {
      payment = payments[0];
      console.log('\nUsing existing payment:');
      console.log(`ID: ${payment.id}`);
      console.log(`Amount: £${payment.amount}`);
      console.log(`Status: ${payment.status}`);
    } else {
      console.error('No payment found for this task.');
      return;
    }
    
    // Check if an invoice already exists for this payment
    const { data: existingInvoices, error: invoicesError } = await supabase
      .from('invoices')
      .select('*')
      .eq('payment_id', payment.id);
    
    if (invoicesError) {
      console.error('Error checking existing invoices:', invoicesError);
      return;
    }
    
    if (existingInvoices && existingInvoices.length > 0) {
      console.log('\nInvoice already exists for this payment:');
      console.log(`ID: ${existingInvoices[0].id}`);
      console.log(`Invoice Number: ${existingInvoices[0].invoice_number}`);
      console.log(`Status: ${existingInvoices[0].status}`);
      
      // Update the existing invoice with the task_id if it's missing
      if (!existingInvoices[0].task_id) {
        console.log('\nUpdating existing invoice with task_id...');
        
        const { data: updatedInvoice, error: updateError } = await supabase
          .from('invoices')
          .update({ task_id: taskId })
          .eq('id', existingInvoices[0].id)
          .select()
          .single();
        
        if (updateError) {
          console.error('Error updating invoice:', updateError);
        } else {
          console.log('Invoice updated successfully:');
          console.log(`ID: ${updatedInvoice.id}`);
          console.log(`Task ID: ${updatedInvoice.task_id}`);
        }
      }
      
      return;
    }
    
    // Get the payer and supplier details
    const { data: payer, error: payerError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payer_id)
      .single();
    
    if (payerError) {
      console.error('Error fetching payer:', payerError);
      return;
    }
    
    const { data: supplier, error: supplierError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', payment.payee_id)
      .single();
    
    if (supplierError) {
      console.error('Error fetching supplier:', supplierError);
      return;
    }
    
    console.log('\nPayer Details:');
    console.log(`ID: ${payer.id}`);
    console.log(`Email: ${payer.email[0]}`);
    
    console.log('\nSupplier Details:');
    console.log(`ID: ${supplier.id}`);
    console.log(`Email: ${supplier.email[0]}`);
    
    // Create or get a Stripe customer for the payer
    let customer;
    
    try {
      // Check if customer already exists
      const customers = await stripe.customers.list({
        email: payer.email[0],
        limit: 1
      });
      
      if (customers.data.length > 0) {
        customer = customers.data[0];
        console.log(`\nUsing existing customer: ${customer.id}`);
      } else {
        // Create a new customer
        customer = await stripe.customers.create({
          email: payer.email[0],
          name: `${payer.first_name} ${payer.last_name}` || payer.email[0],
          metadata: {
            user_id: payer.id
          }
        });
        console.log(`\nCreated new customer: ${customer.id}`);
      }
    } catch (error) {
      console.error('Error creating/getting Stripe customer:', error);
      return;
    }
    
    // Create an invoice item
    const invoiceItem = await stripe.invoiceItems.create({
      customer: customer.id,
      amount: Math.round(payment.amount * 100), // Convert to cents
      currency: payment.currency || 'gbp',
      description: `Payment for: ${task.title}`,
    });
    
    console.log(`\nCreated invoice item: ${invoiceItem.id}`);
    
    // Create an invoice
    const invoice = await stripe.invoices.create({
      customer: customer.id,
      collection_method: 'send_invoice',
      days_until_due: 30,
      metadata: {
        payment_id: payment.id,
        task_id: task.id,
        offer_id: acceptedOffer.id
      }
    });
    
    console.log(`Created invoice: ${invoice.id}`);
    
    // Finalize the invoice
    const finalizedInvoice = await stripe.invoices.finalizeInvoice(invoice.id);
    
    console.log(`Finalized invoice: ${finalizedInvoice.id}`);
    console.log(`Invoice number: ${finalizedInvoice.number}`);
    
    // Send the invoice
    const sentInvoice = await stripe.invoices.sendInvoice(finalizedInvoice.id);
    
    console.log(`Sent invoice: ${sentInvoice.id}`);
    console.log(`Invoice URL: ${sentInvoice.hosted_invoice_url}`);
    
    // Store the invoice in our database
    const { data: dbInvoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        payment_id: payment.id,
        task_id: task.id,
        invoice_number: finalizedInvoice.number,
        invoice_url: finalizedInvoice.hosted_invoice_url,
        stripe_invoice_id: finalizedInvoice.id,
        status: finalizedInvoice.status,
        due_date: finalizedInvoice.due_date ? new Date(finalizedInvoice.due_date * 1000).toISOString() : null
      })
      .select()
      .single();
    
    if (invoiceError) {
      console.error('Error creating invoice record:', invoiceError);
      return;
    }
    
    console.log('\nInvoice record created in database:');
    console.log(`ID: ${dbInvoice.id}`);
    console.log(`Invoice Number: ${dbInvoice.invoice_number}`);
    console.log(`Status: ${dbInvoice.status}`);
    
    console.log('\nManual invoice creation completed successfully.');
  } catch (error) {
    console.error('Error creating manual invoice:', error);
  }
}

createManualInvoice();
