{"name": "vite_react_shadcn_ts", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "node scripts/generate-env-config.cjs && vite build --mode production", "build:production": "node scripts/remove-console-logs.js && node scripts/generate-env-config.cjs && vite build --mode production", "build:dev": "node scripts/generate-env-config.cjs && vite build --mode development", "lint": "eslint .", "preview": "vite preview", "manage-profiles": "node --experimental-modules --es-module-specifier-resolution=node src/scripts/manage-profiles.js", "stripe-server": "node server/index.js", "api-server": "node server/api-server.js", "dev:all": "concurrently \"npm run dev\" \"npm run stripe-server\" \"npm run api-server\"", "dev:all:old": "concurrently \"npm run dev\" \"npm run stripe-server:mock\"", "vercel-dev": "vercel dev", "update-db-schema": "node --experimental-modules --es-module-specifier-resolution=node src/scripts/update-organizations-schema.js", "update-tasks-schema": "node --experimental-modules --es-module-specifier-resolution=node scripts/update-tasks-schema.js", "apply-exec-sql": "node --experimental-modules --es-module-specifier-resolution=node src/scripts/apply-exec-sql-function.js", "apply-secure-functions": "node --experimental-modules --es-module-specifier-resolution=node src/scripts/apply-secure-functions.js", "apply-rls-policies": "node --experimental-modules --es-module-specifier-resolution=node apply-rls-policies.js", "fix-profiles-recursion": "node --experimental-modules --es-module-specifier-resolution=node apply-profiles-fix.js", "setup-stripe-connect": "node --experimental-modules --es-module-specifier-resolution=node apply-stripe-connect-setup.js", "update-location-schema": "node --experimental-modules --es-module-specifier-resolution=node apply-location-schema-updates.js", "migrate-to-getstream": "node --experimental-modules --es-module-specifier-resolution=node src/scripts/migrate-to-getstream.js", "test:getstream": "node src/tests/run-all-getstream-tests.js", "test:getstream:comprehensive": "node src/tests/getstream-comprehensive-test.js", "test:getstream:pwa": "node src/tests/getstream-pwa-test.js", "test:getstream:api": "node src/tests/getstream-api-test.js", "test:getstream:auth": "node --experimental-modules scripts/test-getstream-auth.js", "security:audit": "node scripts/security-audit.js", "security:cleanup": "node scripts/security-cleanup.js", "security:install-hooks": "node scripts/install-git-hooks.js", "security:fix-critical": "node scripts/fix-critical-security-issues.js"}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@stream-io/stream-chat-css": "^5.9.1", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.1.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@types/nodemailer": "^6.4.17", "@types/react-helmet": "^6.1.11", "body-parser": "^2.2.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "concurrently": "^9.1.2", "cors": "^2.8.5", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.3.0", "express": "^5.1.0", "http-proxy-middleware": "^3.0.5", "input-otp": "^1.2.4", "isomorphic-dompurify": "^2.25.0", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "ngrok": "^5.0.0-beta.2", "node-fetch": "^2.7.0", "nodemailer": "^6.10.1", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "recharts": "^2.12.7", "resend": "^4.5.1", "sonner": "^1.5.0", "stream-chat": "^9.1.1", "stream-chat-react": "^13.0.0", "stripe": "^18.0.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vercel": "^41.7.8", "vite": "^5.4.1"}}