// This file is dynamically generated during build time to inject environment variables
// SECURITY: Only public environment variables should be included here
// NEVER hardcode actual API keys in this file!

window.env = {
  // These will be replaced with actual values during build process from environment variables
  VITE_GETSTREAM_API_KEY: process.env.VITE_GETSTREAM_API_KEY || "",
  VITE_SUPABASE_URL: process.env.VITE_SUPABASE_URL || "https://qcnotlojmyvpqbbgoxbc.supabase.co",
  VITE_SUPABASE_ANON_KEY: process.env.VITE_SUPABASE_ANON_KEY || "",
  VITE_STRIPE_PUBLIC_KEY: process.env.VITE_STRIPE_PUBLIC_KEY || "",
  VITE_GOOGLE_MAPS_API_KEY: process.env.VITE_GOOGLE_MAPS_API_KEY || ""
};

// Security check - warn if running with placeholder values in development
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  const missingKeys = Object.entries(window.env)
    .filter(([key, value]) => !value || value.includes('process.env'))
    .map(([key]) => key);

  if (missingKeys.length > 0) {
    console.warn('[SECURITY] Missing environment variables:', missingKeys);
    console.warn('[SECURITY] Make sure to set these in your .env.local file');
  }
}