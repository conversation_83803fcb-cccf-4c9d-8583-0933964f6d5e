// <PERSON>ript to verify that admins can manage offers after applying the new policies
const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Validate environment variables
if (!process.env.SUPABASE_URL || !process.env.SUPABASE_ANON_KEY) {
  console.error('Error: SUPABASE_URL and SUPABASE_ANON_KEY must be set in .env.local');
  process.exit(1);
}

// Create Supabase client with anon key (we'll sign in as an admin user)
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_ANON_KEY
);

// Admin user credentials - replace with actual admin credentials
const ADMIN_EMAIL = process.env.ADMIN_EMAIL || '<EMAIL>';
const ADMIN_PASSWORD = process.env.ADMIN_PASSWORD || 'password';

async function main() {
  try {
    console.log('Starting admin offer management verification...');

    // Step 1: Sign in as admin
    console.log(`Signing in as admin (${ADMIN_EMAIL})...`);
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
    });

    if (authError) {
      console.error('Error signing in as admin:', authError);
      process.exit(1);
    }

    console.log('Successfully signed in as admin');

    // Step 2: Verify admin role
    const { data: profileData, error: profileError } = await supabase
      .from('profiles')
      .select('role, account_type')
      .eq('id', authData.user.id)
      .single();

    if (profileError) {
      console.error('Error fetching admin profile:', profileError);
      process.exit(1);
    }

    if (profileData.role !== 'admin') {
      console.error(`User is not an admin. Current role: ${profileData.role}`);
      process.exit(1);
    }

    console.log('Confirmed user has admin role:', profileData);

    // Step 3: Find a task with awaiting offers
    console.log('Finding a task with awaiting offers...');
    const { data: tasks, error: tasksError } = await supabase
      .from('tasks')
      .select('id, title, user_id')
      .eq('status', 'open')
      .limit(10);

    if (tasksError) {
      console.error('Error fetching tasks:', tasksError);
      process.exit(1);
    }

    if (!tasks || tasks.length === 0) {
      console.error('No open tasks found');
      process.exit(1);
    }

    console.log(`Found ${tasks.length} open tasks`);

    // Step 4: Check for offers on these tasks
    let taskWithOffers = null;
    let offers = [];

    for (const task of tasks) {
      console.log(`Checking offers for task ${task.id} (${task.title})...`);
      
      const { data: taskOffers, error: offersError } = await supabase
        .from('offers')
        .select('*')
        .eq('task_id', task.id);

      if (offersError) {
        console.error(`Error fetching offers for task ${task.id}:`, offersError);
        continue;
      }

      if (taskOffers && taskOffers.length > 0) {
        const awaitingOffers = taskOffers.filter(o => o.status === 'awaiting');
        
        if (awaitingOffers.length > 0) {
          console.log(`Found ${awaitingOffers.length} awaiting offers for task ${task.id}`);
          taskWithOffers = task;
          offers = awaitingOffers;
          break;
        } else {
          console.log(`Task ${task.id} has ${taskOffers.length} offers, but none are awaiting`);
        }
      } else {
        console.log(`No offers found for task ${task.id}`);
      }
    }

    if (!taskWithOffers) {
      console.log('No tasks with awaiting offers found. Creating a test offer...');
      
      // Find a task that's not owned by the admin
      const nonAdminTask = tasks.find(t => t.user_id !== authData.user.id);
      
      if (!nonAdminTask) {
        console.error('No suitable task found for testing');
        process.exit(1);
      }
      
      // Create a test offer
      const { data: newOffer, error: createError } = await supabase
        .from('offers')
        .insert({
          task_id: nonAdminTask.id,
          user_id: authData.user.id, // Admin creates the offer for testing
          amount: 100,
          message: 'Test offer for admin policy verification',
          status: 'awaiting'
        })
        .select()
        .single();
      
      if (createError) {
        console.error('Error creating test offer:', createError);
        process.exit(1);
      }
      
      console.log('Created test offer:', newOffer);
      taskWithOffers = nonAdminTask;
      offers = [newOffer];
    }

    // Step 5: Try to update an offer as admin
    if (offers.length > 0) {
      const offerToUpdate = offers[0];
      console.log(`Attempting to update offer ${offerToUpdate.id} for task ${taskWithOffers.id}...`);
      
      // Check if the admin is the task owner
      const isTaskOwner = taskWithOffers.user_id === authData.user.id;
      console.log(`Admin is ${isTaskOwner ? '' : 'NOT '}the owner of this task`);
      
      // Try to update the offer status
      const newStatus = offerToUpdate.status === 'awaiting' ? 'accepted' : 'awaiting';
      const { data: updatedOffer, error: updateError } = await supabase
        .from('offers')
        .update({ status: newStatus })
        .eq('id', offerToUpdate.id)
        .select()
        .single();
      
      if (updateError) {
        console.error('Error updating offer:', updateError);
        console.error('The admin policies may not be working correctly');
        process.exit(1);
      }
      
      console.log(`Successfully updated offer status from ${offerToUpdate.status} to ${updatedOffer.status}`);
      console.log('Admin offer management is working correctly!');
      
      // If we accepted an offer, update the task status
      if (updatedOffer.status === 'accepted') {
        console.log(`Updating task ${taskWithOffers.id} status to assigned...`);
        
        const { error: taskUpdateError } = await supabase
          .from('tasks')
          .update({
            status: 'assigned',
            assigned_to: offerToUpdate.user_id
          })
          .eq('id', taskWithOffers.id);
        
        if (taskUpdateError) {
          console.error('Error updating task status:', taskUpdateError);
        } else {
          console.log('Task status updated successfully');
        }
      }
    } else {
      console.error('No offers available to test with');
      process.exit(1);
    }

  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

main();
