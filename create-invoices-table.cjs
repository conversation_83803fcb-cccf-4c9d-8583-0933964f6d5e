// Script to create invoices table
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function createInvoicesTable() {
  try {
    console.log('Creating or updating invoices table...');
    
    // Create the invoices table if it doesn't exist
    const { error: createError } = await supabase.rpc('execute_sql', {
      sql_query: `
        CREATE TABLE IF NOT EXISTS public.invoices (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          payment_id UUID REFERENCES public.payments(id),
          invoice_number TEXT,
          stripe_invoice_id TEXT,
          invoice_url TEXT,
          status TEXT,
          due_date TIMESTAMP WITH TIME ZONE,
          paid_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });
    
    if (createError) {
      console.error('Error creating invoices table:', createError);
      
      // Try a different approach with direct SQL
      console.log('Trying direct SQL execution...');
      
      // Execute SQL directly
      const { data, error } = await supabase.from('_sql').select('*').execute(`
        CREATE TABLE IF NOT EXISTS public.invoices (
          id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
          payment_id UUID REFERENCES public.payments(id),
          invoice_number TEXT,
          stripe_invoice_id TEXT,
          invoice_url TEXT,
          status TEXT,
          due_date TIMESTAMP WITH TIME ZONE,
          paid_at TIMESTAMP WITH TIME ZONE,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
        
        -- Add task_id column if it doesn't exist
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'invoices'
            AND column_name = 'task_id'
          ) THEN
            ALTER TABLE public.invoices ADD COLUMN task_id UUID REFERENCES public.tasks(id);
          END IF;
        END
        $$;
      `);
      
      if (error) {
        console.error('Error with direct SQL execution:', error);
        
        // Try using the SQL API
        console.log('Trying SQL API...');
        
        try {
          // Create a temporary SQL file
          const fs = require('fs');
          const sqlFile = 'create_invoices_table.sql';
          
          fs.writeFileSync(sqlFile, `
            CREATE TABLE IF NOT EXISTS public.invoices (
              id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
              payment_id UUID REFERENCES public.payments(id),
              invoice_number TEXT,
              stripe_invoice_id TEXT,
              invoice_url TEXT,
              status TEXT,
              due_date TIMESTAMP WITH TIME ZONE,
              paid_at TIMESTAMP WITH TIME ZONE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              task_id UUID REFERENCES public.tasks(id)
            );
          `);
          
          // Execute the SQL file using the Supabase CLI
          const { execSync } = require('child_process');
          execSync(`npx supabase db execute --file ${sqlFile}`);
          
          console.log('Table created using Supabase CLI');
          
          // Clean up
          fs.unlinkSync(sqlFile);
        } catch (cliError) {
          console.error('Error using Supabase CLI:', cliError);
        }
      } else {
        console.log('Table created using direct SQL execution');
      }
    } else {
      console.log('Invoices table created or updated successfully.');
    }
    
    // Add task_id column to the invoices table
    console.log('\nAdding task_id column to invoices table...');
    
    const { error: alterError } = await supabase.rpc('execute_sql', {
      sql_query: `
        -- Add task_id column if it doesn't exist
        DO $$
        BEGIN
          IF NOT EXISTS (
            SELECT 1
            FROM information_schema.columns
            WHERE table_name = 'invoices'
            AND column_name = 'task_id'
          ) THEN
            ALTER TABLE public.invoices ADD COLUMN task_id UUID REFERENCES public.tasks(id);
          END IF;
        END
        $$;
      `
    });
    
    if (alterError) {
      console.error('Error adding task_id column:', alterError);
    } else {
      console.log('Task_id column added or already exists.');
    }
    
    // Create a test invoice
    console.log('\nCreating a test invoice...');
    
    // Get a payment to use for the test invoice
    const { data: payment, error: paymentError } = await supabase
      .from('payments')
      .select('*')
      .eq('task_id', 'fd946027-b773-43b4-b267-90a049b9458d')
      .limit(1)
      .single();
    
    if (paymentError) {
      console.error('Error fetching payment:', paymentError);
      return;
    }
    
    // Create a test invoice
    const { data: invoice, error: invoiceError } = await supabase
      .from('invoices')
      .insert({
        payment_id: payment.id,
        task_id: payment.task_id,
        invoice_number: 'TEST-' + Math.floor(Math.random() * 10000),
        stripe_invoice_id: 'in_test_' + Math.floor(Math.random() * 10000),
        invoice_url: 'https://example.com/invoice',
        status: 'draft',
        due_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select();
    
    if (invoiceError) {
      console.error('Error creating test invoice:', invoiceError);
    } else {
      console.log('Test invoice created successfully:', invoice);
    }
    
    console.log('\nCheck completed.');
  } catch (error) {
    console.error('Error creating invoices table:', error);
  }
}

createInvoicesTable();
