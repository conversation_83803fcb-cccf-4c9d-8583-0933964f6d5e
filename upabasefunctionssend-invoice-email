warning: in the working copy of 'server/api/stripe-connect.js', <PERSON><PERSON> will be replaced by <PERSON><PERSON> the next time Git touches it
[1mdiff --git a/server/api/stripe-connect.js b/server/api/stripe-connect.js[m
[1mindex 2cd45d3..8ed0e0b 100644[m
[1m--- a/server/api/stripe-connect.js[m
[1m+++ b/server/api/stripe-connect.js[m
[36m@@ -2,723 +2,558 @@[m [mimport express from 'express';[m
 import Stripe from 'stripe';[m
 import { createClient } from '@supabase/supabase-js';[m
 import dotenv from 'dotenv';[m
[31m-import path from 'path';[m
[31m-import { fileURLToPath } from 'url';[m
[32m+[m[32mimport cors from 'cors';[m
 [m
[31m-// Get the directory path[m
[31m-const __filename = fileURLToPath(import.meta.url);[m
[31m-const __dirname = path.dirname(__filename);[m
[31m-const rootDir = path.resolve(__dirname, '../..');[m
[31m-[m
[31m-// Load environment variables[m
 dotenv.config();[m
 [m
[31m-// Also try to load from .env.stripe if it exists[m
[31m-dotenv.config({ path: path.resolve(rootDir, '.env.stripe') });[m
[31m-[m
 const router = express.Router();[m
[32m+[m[32mrouter.use(cors());[m
[32m+[m[32mrouter.use(express.json());[m
 [m
 // Initialize Stripe with the secret key[m
 const stripeSecretKey = process.env.STRIPE_SECRET_KEY;[m
[31m-[m
[31m-console.log('Full environment variables:', process.env);[m
 console.log('Raw Stripe secret key:', stripeSecretKey);[m
[32m+[m[32mconsole.log('Stripe API key:', stripeSecretKey ? `${stripeSecretKey.substring(0, 7)}...${stripeSecretKey.substring(stripeSecretKey.length - 4)}` : 'undefined');[m
[32m+[m[32mconsole.log('API mode:', stripeSecretKey?.startsWith('sk_test') ? 'TEST' : 'LIVE');[m
[32m+[m[32mconsole.log('Key length:', stripeSecretKey?.length);[m
 [m
 if (!stripeSecretKey) {[m
[31m-  console.error('Missing Stripe secret key. Check your environment variables.');[m
[32m+[m[32m  console.error('Stripe secret key is not defined in environment variables');[m
   process.exit(1);[m
 }[m
 [m
[31m-// Clean up the key in case there are any hidden characters or line breaks[m
[31m-const cleanKey = stripeSecretKey.trim().replace(/\s+/g, '');[m
[32m+[m[32mconsole.log('Using Stripe API key:', stripeSecretKey.startsWith('sk_test') ? 'TEST MODE' : 'LIVE MODE');[m
 [m
[31m-// Initialize Stripe with the test secret key[m
[31m-console.log(`Stripe API key: ${cleanKey.substring(0, 8)}...${cleanKey.substring(cleanKey.length - 4)}`);[m
[31m-console.log(`API mode: ${cleanKey.startsWith('sk_test_') ? 'TEST' : 'LIVE'}`);[m
[31m-console.log(`Key length: ${cleanKey.length}`);[m
[32m+[m[32mconst stripe = new Stripe(stripeSecretKey);[m
 [m
[31m-const stripe = new Stripe(cleanKey, {[m
[31m-  apiVersion: '2023-10-16',[m
[31m-});[m
[32m+[m[32m// Initialize Supabase client[m
[32m+[m[32mconst supabaseUrl = process.env.SUPABASE_URL;[m
[32m+[m[32mconst supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;[m
[32m+[m
[32m+[m[32mif (!supabaseUrl || !supabaseServiceRoleKey) {[m
[32m+[m[32m  console.error('Supabase URL or service role key is not defined in environment variables');[m
[32m+[m[32m  process.exit(1);[m
[32m+[m[32m}[m
 [m
[31m-// Check if we're using test mode or live mode[m
[31m-console.log(`Using Stripe API key: ${cleanKey.startsWith('sk_test_') ? 'TEST MODE' : 'LIVE MODE'}`);[m
[32m+[m[32mconst supabase = createClient(supabaseUrl, supabaseServiceRoleKey);[m
 [m
[31m-// Test endpoint to verify Stripe configuration[m
[31m-router.get('/test-config', async (req, res) => {[m
[32m+[m[32m// Create a Stripe Connect Express account link[m
[32m+[m[32mrouter.post('/create-account-link', async (req, res) => {[m
   try {[m
[31m-    // Test the Stripe configuration by retrieving the account balance[m
[31m-    const balance = await stripe.balance.retrieve();[m
[31m-    return res.json({[m
[31m-      success: true,[m
[31m-      mode: cleanKey.startsWith('sk_test_') ? 'TEST' : 'LIVE',[m
[31m-      balance[m
[32m+[m[32m    const { accountId, refreshUrl, returnUrl } = req.body;[m
[32m+[m
[32m+[m[32m    if (!accountId) {[m
[32m+[m[32m      return res.status(400).json({ error: 'Account ID is required' });[m
[32m+[m[32m    }[m
[32m+[m
[32m+[m[32m    const accountLink = await stripe.accountLinks.create({[m
[32m+[m[32m      account: accountId,[m
[32m+[m[32m      refresh_url: refreshUrl || process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL,[m
[32m+[m[32m      return_url: returnUrl || process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL,[m
[32m+[m[32m      type: 'account_onboarding',[m
     });[m
[32m+[m
[32m+[m[32m    res.json({ url: accountLink.url });[m
   } catch (error) {[m
[31m-    console.error('Error testing Stripe configuration:', error);[m
[31m-    return res.status(500).json({[m
[31m-      success: false,[m
[31m-      error: error.message,[m
[31m-      stack: error.stack[m
[31m-    });[m
[32m+[m[32m    console.error('Error creating account link:', error);[m
[32m+[m[32m    res.status(500).json({ error: 'Failed to create account link' });[m
   }[m
 });[m
 [m
[31m-// Initialize Supabase client with service role key[m
[31m-const supabaseUrl = process.env.SUPABASE_URL;[m
[31m-const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;[m
[32m+[m[32m// Create a Stripe Connect Express onboarding link (alias for create-account-link)[m
[32m+[m[32mrouter.post('/onboarding-link', async (req, res) => {[m
[32m+[m[32m  try {[m
[32m+[m[32m    const { accountId, refreshUrl, returnUrl } = req.body;[m
 [m
[31m-if (!supabaseUrl || !supabaseServiceKey) {[m
[31m-  console.error('Missing Supabase URL or service role key. Check your environment variables.');[m
[31m-  process.exit(1);[m
[31m-}[m
[32m+[m[32m    if (!accountId) {[m
[32m+[m[32m      return res.status(400).json({ error: 'Account ID is required' });[m
[32m+[m[32m    }[m
[32m+[m
[32m+[m[32m    console.log('Creating onboarding link for account:', accountId);[m
[32m+[m
[32m+[m[32m    const accountLink = await stripe.accountLinks.create({[m
[32m+[m[32m      account: accountId,[m
[32m+[m[32m      refresh_url: refreshUrl || process.env.STRIPE_CONNECT_EXPRESS_REFRESH_URL,[m
[32m+[m[32m      return_url: returnUrl || process.env.STRIPE_CONNECT_EXPRESS_RETURN_URL,[m
[32m+[m[32m      type: 'account_onboarding',[m
[32m+[m[32m    });[m
 [m
[31m-const supabase = createClient(supabaseUrl, supabaseServiceKey);[m
[32m+[m[32m    console.log('Created onboarding link:', accountLink.url);[m
[32m+[m[32m    res.json({ url: accountLink.url });[m
[32m+[m[32m  } catch (error) {[m
[32m+[m[32m    console.error('Error creating onboarding link:', error);[m
[32m+[m[32m    res.status(500).json({ error: 'Failed to create onboarding link' });[m
[32m+[m[32m  }[m
[32m+[m[32m});[m
 [m
 // Create a Stripe Connect Express account[m
 router.post('/create-account', async (req, res) => {[m
   try {[m
[31m-    const { userId } = req.body;[m
[32m+[m[32m    const { userId, email, country = 'GB' } = req.body;[m
 [m
[31m-    if (!userId) {[m
[31m-      return res.status(400).json({ error: 'Missing userId parameter' });[m
[32m+[m[32m    if (!userId || !email) {[m
[32m+[m[32m      return res.status(400).json({ error: 'User ID and email are required' });[m
     }[m
 [m
[31m-    console.log(`Creating Express account for user ${userId}`);[m
[31m-[m
[31m-    // First check if user already has an active account (excluding deleted accounts)[m
[31m-    const { data: existingActiveAccount, error: fetchActiveError } = await supabase[m
[32m+[m[32m    // Check if the user already has a Stripe account[m
[32m+[m[32m    const { data: existingAccounts } = await supabase[m
       .from('stripe_accounts')[m
       .select('*')[m
[31m-      .eq('user_id', userId)[m
[31m-      .neq('account_status', 'deleted')[m
[31m-      .maybeSingle();[m
[32m+[m[32m      .eq('user_id', userId);[m
 [m
[31m-    if (fetchActiveError) {[m
[31m-      console.error('Error checking for existing active account:', fetchActiveError);[m
[31m-      return res.status(500).json({ error: 'Error checking for existing account' });[m
[32m+[m[32m    if (existingAccounts && existingAccounts.length > 0) {[m
[32m+[m[32m      return res.json({ accountId: existingAccounts[0].account_id });[m
     }[m
 [m
[31m-    if (existingActiveAccount) {[m
[31m-      console.log(`User ${userId} already has an active Stripe account:`, existingActiveAccount);[m
[31m-      return res.json({ account: existingActiveAccount });[m
[31m-    }[m
[32m+[m[32m    // Create a new Stripe Connect Express account[m
[32m+[m[32m    const account = await stripe.accounts.create({[m
[32m+[m[32m      type: 'express',[m
[32m+[m[32m      country,[m
[32m+[m[32m      email,[m
[32m+[m[32m      capabilities: {[m
[32m+[m[32m        card_payments: { requested: true },[m
[32m+[m[32m        transfers: { requested: true },[m
[32m+[m[32m      },[m
[32m+[m[32m      business_type: 'individual',[m
[32m+[m[32m      business_profile: {[m
[32m+[m[32m        mcc: '8299', // Educational Services[m
[32m+[m[32m        url: 'https://lovable.dev',[m
[32m+[m[32m      },[m
[32m+[m[32m    });[m
 [m
[31m-    // Now check if user has a deleted account that we can update[m
[31m-    const { data: existingDeletedAccount, error: fetchDeletedError } = await supabase[m
[32m+[m[32m    // Store the account in the database[m
[32m+[m[32m    const { data, error } = await supabase[m
       .from('stripe_accounts')[m
[31m-      .select('*')[m
[31m-      .eq('user_id', userId)[m
[31m-      .eq('account_status', 'deleted')[m
[31m-      .maybeSingle();[m
[32m+[m[32m      .insert({[m
[32m+[m[32m        user_id: userId,[m
[32m+[m[32m        account_id: account.id,[m
[32m+[m[32m        account_type: 'express',[m
[32m+[m[32m        account_status: 'pending',[m
[32m+[m[32m      });[m
 [m
[31m-    if (fetchDeletedError) {[m
[31m-      console.error('Error checking for existing deleted account:', fetchDeletedError);[m
[31m-      // Continue anyway, as this is not a critical error[m
[32m+[m[32m    if (error) {[m
[32m+[m[32m      console.error('Error storing Stripe account in database:', error);[m
[32m+[m[32m      return res.status(500).json({ error: 'Failed to store Stripe account' });[m
     }[m
 [m
[31m-    // If there's a deleted account, we'll update it instead of creating a new one[m
[31m-    if (existingDeletedAccount) {[m
[31m-      console.log(`User ${userId} has a deleted Stripe account that will be updated:`, existingDeletedAccount);[m
[31m-    }[m
[32m+[m[32m    res.json({ accountId: account.id });[m
[32m+[m[32m  } catch (error) {[m
[32m+[m[32m    console.error('Error creating Stripe account:', error);[m
[32m+[m[32m    res.status(500).json({ error: 'Failed to create Stripe account' });[m
[32m+[m[32m  }[m
[32m+[m[32m});[m
 [m
[31m-    // Get user profile for business information[m
[31m-    const { data: profile, error: profileError } = await supabase[m
[31m-      .from('profiles')[m
[31m-      .select('*')[m
[31m-      .eq('id', userId)[m
[31m-      .single();[m
[32m+[m[32m// Get Stripe Connect Express account status[m
[32m+[m[32mrouter.get('/account-status/:accountId', async (req, res) => {[m
[32m+[m[32m  try {[m
[32m+[m[32m    const { accountId } = req.params;[m
[32m+[m[32m    console.log('Getting status for account', accountId);[m
 [m
[31m-    if (profileError) {[m
[31m-      console.error('Error fetching user profile:', profileError);[m
[31m-      return res.status(500).json({ error: 'Error fetching user profile' });[m
[32m+[m[32m    if (!accountId) {[m
[32m+[m[32m      return res.status(400).json({ error: 'Account ID is required' });[m
     }[m
 [m
[31m-    // Create a real Stripe Connect Express account[m
[31m-    console.log('Creating Stripe Connect Express account...');[m
[32m+[m[32m    console.log('Retrieving Stripe Connect Express account status...');[m
[32m+[m[32m    const account = await stripe.accounts.retrieve(accountId);[m
 [m
[31m-    try {[m
[31m-      const account = await stripe.accounts.create({[m
[31m-        type: 'express',[m
[31m-        capabilities: {[m
[31m-          card_payments: { requested: true },[m
[31m-          transfers: { requested: true },[m
[31m-        },[m
[31m-        business_type: 'individual', // Default to individual, can be updated later[m
[31m-        metadata: {[m
[31m-          user_id: userId,[m
[31m-        },[m
[31m-      });[m
[32m+[m[32m    // Update the account status in the database[m
[32m+[m[32m    const accountStatus = account.charges_enabled ? 'active' : 'pending';[m
 [m
[31m-      console.log(`Created Stripe account ${account.id} for user ${userId}`);[m
[31m-[m
[31m-      let data, error;[m
[31m-[m
[31m-      // If there's a deleted account, update it instead of creating a new one[m
[31m-      if (existingDeletedAccount) {[m
[31m-        console.log(`Updating existing deleted account ${existingDeletedAccount.id} with new Stripe account ID ${account.id}`);[m
[31m-[m
[31m-        const { data: updatedData, error: updateError } = await supabase[m
[31m-          .from('stripe_accounts')[m
[31m-          .update({[m
[31m-            account_id: account.id,[m
[31m-            account_type: 'express',[m
[31m-            charges_enabled: account.charges_enabled,[m
[31m-            payouts_enabled: account.payouts_enabled,[m
[31m-            account_status: 'pending',[m
[31m-            updated_at: new Date().toISOString()[m
[31m-          })[m
[31m-          .eq('id', existingDeletedAccount.id)[m
[31m-          .select();[m
[31m-[m
[31m-        data = updatedData;[m
[31m-        error = updateError;[m
[31m-      } else {[m
[31m-        // Store the account in our database as a new record[m
[31m-        const { data: insertedData, error: insertError } = await supabase[m
[31m-          .from('stripe_accounts')[m
[31m-          .insert([{[m
[31m-            user_id: userId,[m
[31m-            account_id: account.id,[m
[31m-            account_type: 'express',[m
[31m-            charges_enabled: account.charges_enabled,[m
[31m-            payouts_enabled: account.payouts_enabled,[m
[31m-            account_status: 'pending',[m
[31m-          }])[m
[31m-          .select();[m
[31m-[m
[31m-        data = insertedData;[m
[31m-        error = insertError;[m
[31m-      }[m
[32m+[m[32m    const { data, error } = await supabase[m
[32m+[m[32m      .from('stripe_accounts')[m
[32m+[m[32m      .update({[m
[32m+[m[32m        charges_enabled: account.charges_enabled,[m
[32m+[m[32m        payouts_enabled: account.payouts_enabled,[m
[32m+[m[32m        account_status: accountStatus,[m
[32m+[m[32m      })[m
[32m+[m[32m      .eq('account_id', accountId);[m
 [m
     if (error) {[m
[31m-      console.error('Error storing mock Stripe account:', error);[m
[31m-      return res.status(500).json({ error: 'Error storing mock Stripe account' });[m
[32m+[m[32m      console.error('Error updating Stripe account status in database:', error);[m
     }[m
 [m
[31m-    // Update the user's profile with the Stripe account ID[m
[31m-    await supabase[m
[31m-      .from('profiles')[m
[31m-      .update({ stripe_account_id: account.id })[m
[31m-      .eq('id', userId);[m
[31m-[m
[31m-    return res.json({ account: data[0] });[m
[31m-    } catch (stripeError) {[m
[31m-      console.error('Error creating Stripe account:', stripeError);[m
[31m-      return res.status(500).json({ error: 'Error creating Stripe account' });[m
[31m-    }[m
[32m+[m[32m    res.json({[m
[32m+[m[32m      id: account.id,[m
[32m+[m[32m      charges_enabled: account.charges_enabled,[m
[32m+[m[32m      payouts_enabled: account.payouts_enabled,[m
[32m+[m[32m      requirements: account.requirements,[m
[32m+[m[32m      status: accountStatus,[m
[32m+[m[32m    });[m
   } catch (error) {[m
[31m-    console.error('Error creating Express account:', error);[m
[31m-    return res.status(500).json({ error: 'Error creating Exp