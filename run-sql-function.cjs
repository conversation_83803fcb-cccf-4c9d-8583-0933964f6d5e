// <PERSON><PERSON><PERSON> to run the SQL function
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials. Check your environment variables.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runSqlFunction() {
  try {
    console.log('Running SQL function...');
    
    // Read the SQL file
    const sql = fs.readFileSync('sql/update_user_from_auth.sql', 'utf8');
    
    // Run the SQL
    const { error } = await supabase.rpc('exec_sql', { sql });
    
    if (error) {
      console.error('Error running SQL:', error);
      
      // Try an alternative approach
      console.log('Trying alternative approach...');
      
      // Create the function directly
      const createFunctionSql = `
        CREATE OR REPLACE FUNCTION update_user_email_from_auth(user_id UUID, auth_email TEXT)
        RETURNS VOID AS $$
        BEGIN
          -- Update the profile with the provided email
          UPDATE profiles
          SET
            email = ARRAY[auth_email]
          WHERE
            id = user_id
            AND (email IS NULL OR email = '{}' OR email = '{null}');
            
          RAISE NOTICE 'Updated email for user % to %', user_id, auth_email;
        END;
        $$ LANGUAGE plpgsql;
      `;
      
      const { data, error: createError } = await supabase.from('_sql').select('*').eq('query', createFunctionSql).limit(1);
      
      if (createError) {
        console.error('Error creating function:', createError);
        return;
      }
      
      console.log('Function created successfully!');
    } else {
      console.log('SQL function ran successfully!');
    }
    
    // Test the function
    const supplierId = '18625693-2496-45a4-a1d8-675a9bf2683b';
    
    // Get the user from the auth.users table
    const { data: authUser, error: authError } = await supabase.auth.admin.getUserById(supplierId);
    
    if (authError) {
      console.error('Error fetching user from auth system:', authError);
      return;
    }
    
    if (!authUser || !authUser.user) {
      console.log(`No user found in auth system with ID: ${supplierId}`);
      return;
    }
    
    const authEmail = authUser.user.email;
    console.log(`Found email in auth system: ${authEmail}`);
    
    // Run the updated SQL function
    const { data: updateResult, error: updateError } = await supabase
      .rpc('update_user_email_from_auth', {
        user_id: supplierId,
        auth_email: authEmail
      });
    
    if (updateError) {
      console.error('Error running update_user_email_from_auth:', updateError);
      return;
    }
    
    console.log('Successfully ran update_user_email_from_auth function.');
    
    // Verify the profile email was updated
    const { data: updatedProfile, error: updatedProfileError } = await supabase
      .from('profiles')
      .select('email')
      .eq('id', supplierId)
      .single();
    
    if (updatedProfileError) {
      console.error('Error fetching updated profile:', updatedProfileError);
      return;
    }
    
    const updatedProfileEmail = Array.isArray(updatedProfile.email) ? updatedProfile.email[0] : updatedProfile.email;
    console.log(`Updated profile email: ${updatedProfileEmail}`);
    
    if (updatedProfileEmail === authEmail) {
      console.log('✅ Email updated correctly!');
    } else {
      console.log('❌ Email not updated correctly!');
    }
  } catch (error) {
    console.error('Error running SQL function:', error);
  }
}

runSqlFunction();
