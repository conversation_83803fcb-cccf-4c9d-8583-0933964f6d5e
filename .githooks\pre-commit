#!/bin/sh
#
# Pre-commit hook to prevent committing API keys and secrets
# This hook scans staged files for potential secrets before allowing commit
#

# Colors for output
RED='\033[0;31m'
YELLOW='\033[1;33m'
GREEN='\033[0;32m'
NC='\033[0m' # No Color

echo "${GREEN}🔒 Running security pre-commit checks...${NC}"

# Flag to track if any secrets are found
SECRETS_FOUND=0

# Patterns to search for (case-insensitive)
SECRET_PATTERNS=(
    # API Keys
    "sk_live_[a-zA-Z0-9]{24,}"
    "sk_test_[a-zA-Z0-9]{24,}"
    "pk_live_[a-zA-Z0-9]{24,}"
    "pk_test_[a-zA-Z0-9]{24,}"
    # Google API Keys
    "AIza[0-9A-Za-z\\-_]{35}"
    # GetStream API Keys (specific patterns)
    "qeaycbch5vhf"
    "e379cn39jrwz"
    # JWT Tokens
    "eyJ[A-Za-z0-9-_=]+\.[A-Za-z0-9-_=]+\.?[A-Za-z0-9-_.+/=]*"
    # Generic API key patterns
    "[a-zA-Z0-9]{32,}"
    # Supabase anon keys (specific pattern)
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\."
    # Common secret keywords
    "password.*=.*['\"][^'\"]{8,}['\"]"
    "secret.*=.*['\"][^'\"]{8,}['\"]"
    "token.*=.*['\"][^'\"]{8,}['\"]"
)

# Files to always check (even if not in patterns)
CRITICAL_FILES=(
    "public/env-config.js"
    "index.html"
    "vite.config.ts"
    "lovable.config.js"
)

# Get list of staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

if [ -z "$STAGED_FILES" ]; then
    echo "${GREEN}✅ No staged files to check${NC}"
    exit 0
fi

echo "Checking staged files for secrets..."

# Check each staged file
for FILE in $STAGED_FILES; do
    # Skip binary files and certain file types
    if [[ "$FILE" =~ \.(jpg|jpeg|png|gif|ico|pdf|zip|tar|gz|exe|dll|so|dylib)$ ]]; then
        continue
    fi
    
    # Skip node_modules and other directories
    if [[ "$FILE" =~ ^(node_modules|dist|build|\.git)/ ]]; then
        continue
    fi
    
    # Check if file exists (might be deleted)
    if [ ! -f "$FILE" ]; then
        continue
    fi
    
    echo "  Checking: $FILE"
    
    # Get the staged content of the file
    STAGED_CONTENT=$(git show ":$FILE" 2>/dev/null)
    
    if [ $? -ne 0 ]; then
        continue
    fi
    
    # Check for each secret pattern
    for PATTERN in "${SECRET_PATTERNS[@]}"; do
        MATCHES=$(echo "$STAGED_CONTENT" | grep -iE "$PATTERN" | head -5)
        
        if [ ! -z "$MATCHES" ]; then
            echo "${RED}🚨 SECURITY ALERT: Potential secret found in $FILE${NC}"
            echo "${YELLOW}Pattern: $PATTERN${NC}"
            echo "${YELLOW}Matches:${NC}"
            echo "$MATCHES" | sed 's/^/    /'
            echo ""
            SECRETS_FOUND=1
        fi
    done
    
    # Special check for critical files
    for CRITICAL_FILE in "${CRITICAL_FILES[@]}"; do
        if [[ "$FILE" == "$CRITICAL_FILE" ]]; then
            # Check for any hardcoded values that look like API keys
            HARDCODED_VALUES=$(echo "$STAGED_CONTENT" | grep -E ':\s*"[a-zA-Z0-9_-]{20,}"' | grep -v 'process.env' | grep -v 'PLACEHOLDER')
            
            if [ ! -z "$HARDCODED_VALUES" ]; then
                echo "${RED}🚨 CRITICAL: Hardcoded values in critical file $FILE${NC}"
                echo "${YELLOW}Values:${NC}"
                echo "$HARDCODED_VALUES" | sed 's/^/    /'
                echo ""
                SECRETS_FOUND=1
            fi
        fi
    done
done

# Check for environment variable files that shouldn't be committed
for FILE in $STAGED_FILES; do
    if [[ "$FILE" =~ \.env$ ]] || [[ "$FILE" =~ \.env\. ]]; then
        if [[ ! "$FILE" =~ \.env\.example$ ]]; then
            echo "${RED}🚨 SECURITY ALERT: Environment file should not be committed: $FILE${NC}"
            SECRETS_FOUND=1
        fi
    fi
done

# Final result
if [ $SECRETS_FOUND -eq 1 ]; then
    echo ""
    echo "${RED}❌ COMMIT BLOCKED: Potential secrets detected!${NC}"
    echo ""
    echo "${YELLOW}To fix this:${NC}"
    echo "1. Remove any hardcoded API keys, passwords, or secrets"
    echo "2. Use environment variables instead"
    echo "3. Add sensitive files to .gitignore"
    echo "4. If this is a false positive, you can bypass with: git commit --no-verify"
    echo ""
    echo "${YELLOW}For help with environment variables, see:${NC}"
    echo "- .env.example for reference"
    echo "- scripts/generate-env-config.cjs for build-time injection"
    echo ""
    exit 1
else
    echo "${GREEN}✅ Security check passed - no secrets detected${NC}"
    exit 0
fi
