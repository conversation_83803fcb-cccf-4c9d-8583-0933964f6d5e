import { createClient } from '@supabase/supabase-js';
import * as dotenv from 'dotenv';
dotenv.config();

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase URL or service role key. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runAcceptInvitation() {
  const userId = 'c28e4376-d1f6-4ead-8c05-f96cd3959d81';
  
  console.log('Running accept_invitation for user ID:', userId);
  
  // Get user info
  const { data: userData, error: userError } = await supabase.auth.admin.getUserById(userId);
  
  if (userError) {
    console.error('Error fetching user info:', userError);
    return;
  }
  
  // Get invitation info
  const { data: invitations, error: invitationError } = await supabase
    .from('user_invitations')
    .select('*')
    .eq('email', userData.user.email)
    .order('created_at', { ascending: false });
  
  if (invitationError || !invitations || invitations.length === 0) {
    console.error('Error fetching invitation info or no invitations found');
    return;
  }
  
  const latestInvitation = invitations[0];
  console.log('Latest invitation:', latestInvitation);
  
  // First, check the current profile state
  const { data: profileBefore, error: profileBeforeError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (profileBeforeError) {
    console.error('Error fetching profile before update:', profileBeforeError);
  } else {
    console.log('Profile before update:', profileBefore);
  }
  
  // Call the accept_invitation function
  console.log('Calling accept_invitation function...');
  const { data: result, error: funcError } = await supabase.rpc('accept_invitation', {
    token_param: latestInvitation.token,
    user_id_param: userId
  });
  
  if (funcError) {
    console.error('Error calling accept_invitation function:', funcError);
    
    // Try direct update as a fallback
    console.log('Trying direct update as fallback...');
    const { error: updateError } = await supabase
      .from('profiles')
      .update({
        organization_id: latestInvitation.organization_id,
        role: latestInvitation.role,
        email: [userData.user.email]
      })
      .eq('id', userId);
    
    if (updateError) {
      console.error('Error with direct update:', updateError);
    } else {
      console.log('Direct update successful');
    }
  } else {
    console.log('Function result:', result);
  }
  
  // Check the profile after the update
  const { data: profileAfter, error: profileAfterError } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (profileAfterError) {
    console.error('Error fetching profile after update:', profileAfterError);
  } else {
    console.log('Profile after update:', profileAfter);
    
    // Check if the update was successful
    if (profileAfter.organization_id === latestInvitation.organization_id &&
        profileAfter.role === latestInvitation.role &&
        profileAfter.email && profileAfter.email.includes(userData.user.email)) {
      console.log('✅ Profile successfully updated with invitation data');
    } else {
      console.log('❌ Profile update incomplete or incorrect');
    }
  }
  
  // Check if the invitation status was updated
  const { data: invitationAfter, error: invitationAfterError } = await supabase
    .from('user_invitations')
    .select('*')
    .eq('id', latestInvitation.id)
    .single();
  
  if (invitationAfterError) {
    console.error('Error fetching invitation after update:', invitationAfterError);
  } else {
    console.log('Invitation after update:', invitationAfter);
    
    if (invitationAfter.status === 'accepted') {
      console.log('✅ Invitation status successfully updated to accepted');
    } else {
      console.log('❌ Invitation status not updated to accepted');
    }
  }
}

runAcceptInvitation().catch(console.error).finally(() => process.exit(0));
